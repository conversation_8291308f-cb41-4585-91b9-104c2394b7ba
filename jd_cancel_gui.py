#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
京东取消订单微信通知GUI界面
支持定时获取已取消订单的京东物流单号并发送微信通知
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
from datetime import datetime, timedelta
from jd_cancel_orders import JDCancelOrdersManager
from jd_cancel_manager_v2 import JDCancelManagerV2
from wechat_sender import WeChatConfig

class JDCancelGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("京东取消订单微信通知工具")
        self.root.geometry("900x700")
        
        # 配置文件路径
        self.config_file = "jd_cancel_config.json"
        
        # 初始化变量
        self.is_running = False
        self.timer_thread = None
        self.manager = None
        
        # 配置变量
        self.wechat_webhook_url = tk.StringVar(value="")
        self.wechat_contact = tk.StringVar(value="")
        self.wechat_send_mode = tk.StringVar(value="local")  # local 或 webhook
        self.timer_mode = tk.StringVar(value="daily")  # daily 或 interval
        self.interval_minutes = tk.IntVar(value=60)
        self.daily_hour = tk.IntVar(value=9)
        self.daily_minute = tk.IntVar(value=0)
        self.auto_start = tk.BooleanVar(value=False)
        
        # 多个每日执行时间
        self.daily_times = []
        
        # 加载配置
        self.load_config()
        
        self.setup_ui()
        self.setup_status()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 微信配置区域
        wechat_frame = ttk.LabelFrame(main_frame, text="微信消息发送配置", padding="10")
        wechat_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 发送方式选择
        ttk.Label(wechat_frame, text="发送方式:").grid(row=0, column=0, sticky=tk.W, pady=2)
        send_mode_frame = ttk.Frame(wechat_frame)
        send_mode_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Radiobutton(send_mode_frame, text="本地微信", variable=self.wechat_send_mode, value="local", command=self.on_send_mode_changed).grid(row=0, column=0)
        ttk.Radiobutton(send_mode_frame, text="企业微信机器人", variable=self.wechat_send_mode, value="webhook", command=self.on_send_mode_changed).grid(row=0, column=1, padx=(10, 0))

        # 联系人/群名称
        ttk.Label(wechat_frame, text="联系人/群名称:").grid(row=1, column=0, sticky=tk.W, pady=2)
        contact_entry = ttk.Entry(wechat_frame, textvariable=self.wechat_contact, width=30)
        contact_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # Webhook URL (仅企业微信机器人模式显示)
        self.webhook_label = ttk.Label(wechat_frame, text="机器人Webhook URL:")
        self.webhook_entry = ttk.Entry(wechat_frame, textvariable=self.wechat_webhook_url, width=60)

        # 测试按钮
        ttk.Button(wechat_frame, text="测试微信连接", command=self.test_wechat).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=5)

        wechat_frame.columnconfigure(1, weight=1)

        # 初始化显示
        self.on_send_mode_changed()
        
        # 定时配置区域
        timer_frame = ttk.LabelFrame(main_frame, text="定时设置", padding="10")
        timer_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 定时模式选择
        ttk.Label(timer_frame, text="定时模式:").grid(row=0, column=0, sticky=tk.W, pady=2)
        mode_frame = ttk.Frame(timer_frame)
        mode_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Radiobutton(mode_frame, text="间隔执行", variable=self.timer_mode, value="interval").grid(row=0, column=0)
        ttk.Radiobutton(mode_frame, text="每日定时", variable=self.timer_mode, value="daily").grid(row=0, column=1, padx=(10, 0))
        
        # 间隔设置
        ttk.Label(timer_frame, text="间隔时间(分钟):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Spinbox(timer_frame, from_=1, to=1440, textvariable=self.interval_minutes, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 每日定时设置
        daily_frame = ttk.Frame(timer_frame)
        daily_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(daily_frame, text="每日执行时间:").grid(row=0, column=0, sticky=tk.W)
        
        # 时间选择
        time_select_frame = ttk.Frame(daily_frame)
        time_select_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        ttk.Spinbox(time_select_frame, from_=0, to=23, textvariable=self.daily_hour, width=5).grid(row=0, column=0)
        ttk.Label(time_select_frame, text="时").grid(row=0, column=1, padx=(2, 5))
        ttk.Spinbox(time_select_frame, from_=0, to=59, textvariable=self.daily_minute, width=5).grid(row=0, column=2)
        ttk.Label(time_select_frame, text="分").grid(row=0, column=3, padx=(2, 10))
        ttk.Button(time_select_frame, text="添加时间", command=self.add_daily_time).grid(row=0, column=4, padx=(5, 0))
        
        # 时间列表显示
        self.time_list_frame = ttk.Frame(daily_frame)
        self.time_list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 自动启动
        auto_frame = ttk.Frame(timer_frame)
        auto_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        ttk.Checkbutton(auto_frame, text="程序启动时自动开始定时任务", variable=self.auto_start, command=self.on_auto_start_changed).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(auto_frame, text="保存配置", command=self.save_config_manual).grid(row=0, column=1, padx=(10, 0))
        
        timer_frame.columnconfigure(1, weight=1)
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="任务控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        self.start_button = ttk.Button(button_frame, text="开始定时任务", command=self.start_timer)
        self.start_button.grid(row=0, column=0, padx=(0, 5))
        
        self.stop_button = ttk.Button(button_frame, text="停止定时任务", command=self.stop_timer, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=5)
        
        self.manual_button = ttk.Button(button_frame, text="立即执行一次", command=self.manual_execute)
        self.manual_button.grid(row=0, column=2, padx=5)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=2)
        
        self.mode_label = ttk.Label(status_frame, text="执行模式: --")
        self.mode_label.grid(row=1, column=0, sticky=tk.W, pady=2)
        
        self.next_run_label = ttk.Label(status_frame, text="下次运行: --")
        self.next_run_label.grid(row=2, column=0, sticky=tk.W, pady=2)
        
        self.last_run_label = ttk.Label(status_frame, text="上次运行: --")
        self.last_run_label.grid(row=3, column=0, sticky=tk.W, pady=2)
        
        self.result_label = ttk.Label(status_frame, text="上次结果: --")
        self.result_label.grid(row=4, column=0, sticky=tk.W, pady=2)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def setup_status(self):
        """初始化状态"""
        self.update_time_list_display()

        # 确保log_text已创建后再记录日志
        if hasattr(self, 'log_text'):
            self.log("程序启动完成")
            self.log(f"配置文件: {self.config_file}")

            if self.timer_mode.get() == "daily":
                self.log(f"每日执行时间: {', '.join(self.daily_times)}")
            else:
                self.log(f"间隔执行: 每{self.interval_minutes.get()}分钟")

            # 如果设置了自动启动，则自动开始
            if self.auto_start.get():
                self.log("⏰ 自动启动已启用，3秒后开始定时任务...")
                self.root.after(3000, self.auto_start_timer)微信连接测试 - 2025-07-16 15:25:43
            
            else:
                self.log("💡 提示: 可在配置中启用自动启动功能")
    
    def auto_start_timer(self):
        """自动启动定时任务"""
        try:
            # 验证基本配置
            if not self.wechat_webhook_url.get():
                self.log("❌ 自动启动失败: 未设置微信机器人webhook地址")
                return
            
            if self.timer_mode.get() == "daily" and not self.daily_times:
                self.log("❌ 自动启动失败: 每日定时模式需要至少设置一个执行时间")
                return
            
            # 启动定时任务
            self.start_timer()
            self.log("🚀 自动启动成功！")
            
        except Exception as e:
            self.log(f"❌ 自动启动失败: {e}")
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", f"{len(lines)-500}.0")
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 加载配置到变量
                self.wechat_webhook_url.set(config.get('wechat_webhook_url', ''))
                self.wechat_contact.set(config.get('wechat_contact', ''))
                self.wechat_send_mode.set(config.get('wechat_send_mode', 'local'))
                self.timer_mode.set(config.get('timer_mode', 'daily'))
                self.interval_minutes.set(config.get('interval_minutes', 60))
                self.daily_hour.set(config.get('daily_hour', 9))
                self.daily_minute.set(config.get('daily_minute', 0))
                self.auto_start.set(config.get('auto_start', False))
                self.daily_times = config.get('daily_times', [])
                
                print(f"✅ 配置加载成功: {self.config_file}")
            else:
                # 使用默认配置
                self.daily_times = ["09:00", "14:00", "18:00"]
                print("📋 使用默认配置")
                
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            self.daily_times = ["09:00", "14:00", "18:00"]

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'wechat_webhook_url': self.wechat_webhook_url.get(),
                'wechat_contact': self.wechat_contact.get(),
                'wechat_send_mode': self.wechat_send_mode.get(),
                'timer_mode': self.timer_mode.get(),
                'interval_minutes': self.interval_minutes.get(),
                'daily_hour': self.daily_hour.get(),
                'daily_minute': self.daily_minute.get(),
                'auto_start': self.auto_start.get(),
                'daily_times': self.daily_times,
                'last_saved': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"✅ 配置保存成功: {self.config_file}")

        except Exception as e:
            print(f"❌ 配置保存失败: {e}")

    def save_config_manual(self):
        """手动保存配置"""
        self.save_config()
        self.log("✅ 配置已手动保存")
        messagebox.showinfo("提示", "配置保存成功！")

    def on_auto_start_changed(self):
        """自动启动选项变化时的处理"""
        self.log(f"自动启动设置: {'已启用' if self.auto_start.get() else '已禁用'}")
        self.save_config()

    def on_send_mode_changed(self):
        """发送方式变化时的处理"""
        mode = self.wechat_send_mode.get()

        if mode == "local":
            # 本地微信模式 - 隐藏webhook配置
            self.webhook_label.grid_remove()
            self.webhook_entry.grid_remove()
            # 只有在log_text存在时才记录日志
            if hasattr(self, 'log_text'):
                self.log("切换到本地微信发送模式")
        else:
            # 企业微信机器人模式 - 显示webhook配置
            self.webhook_label.grid(row=2, column=0, sticky=tk.W, pady=2)
            self.webhook_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
            # 只有在log_text存在时才记录日志
            if hasattr(self, 'log_text'):
                self.log("切换到企业微信机器人模式")

        self.save_config()

    def on_closing(self):
        """窗口关闭事件处理"""
        if self.is_running:
            self.stop_timer()
        self.save_config()
        self.log("程序正在关闭...")
        self.root.destroy()

    def add_daily_time(self):
        """添加每日执行时间"""
        hour = self.daily_hour.get()
        minute = self.daily_minute.get()
        time_str = f"{hour:02d}:{minute:02d}"

        if time_str not in self.daily_times:
            self.daily_times.append(time_str)
            self.daily_times.sort()
            self.update_time_list_display()
            self.log(f"添加每日执行时间: {time_str}")
            self.save_config()
        else:
            messagebox.showwarning("警告", f"时间 {time_str} 已存在")

    def remove_daily_time(self, time_str):
        """删除每日执行时间"""
        if time_str in self.daily_times:
            self.daily_times.remove(time_str)
            self.update_time_list_display()
            self.log(f"删除每日执行时间: {time_str}")
            self.save_config()

    def update_time_list_display(self):
        """更新时间列表显示"""
        # 清除现有显示
        for widget in self.time_list_frame.winfo_children():
            widget.destroy()

        if self.daily_times:
            ttk.Label(self.time_list_frame, text="已设置的执行时间:").grid(row=0, column=0, sticky=tk.W, pady=2)

            for i, time_str in enumerate(self.daily_times):
                time_frame = ttk.Frame(self.time_list_frame)
                time_frame.grid(row=i+1, column=0, sticky=tk.W, pady=1)

                ttk.Label(time_frame, text=f"• {time_str}").grid(row=0, column=0, sticky=tk.W)
                ttk.Button(time_frame, text="删除", command=lambda t=time_str: self.remove_daily_time(t), width=6).grid(row=0, column=1, padx=(10, 0))

    def test_wechat(self):
        """测试微信连接"""
        self.log("正在测试微信连接...")

        def test_thread():
            try:
                contact = self.wechat_contact.get()
                if not contact:
                    self.log("❌ 请先设置联系人名称")
                    messagebox.showerror("错误", "请先设置联系人名称")
                    return

                # 根据发送方式创建管理器
                if self.wechat_send_mode.get() == "local":
                    # 本地微信模式
                    temp_manager = JDCancelManagerV2(
                        wechat_contact=contact,
                        use_local_wechat=True
                    )
                else:
                    # 企业微信机器人模式
                    webhook_url = self.wechat_webhook_url.get()
                    if not webhook_url:
                        self.log("❌ 请先设置微信机器人webhook地址")
                        messagebox.showerror("错误", "请先设置微信机器人webhook地址")
                        return

                    temp_manager = JDCancelManagerV2(
                        wechat_contact=contact,
                        use_local_wechat=False,
                        webhook_url=webhook_url
                    )

                if temp_manager.test_wechat_connection():
                    self.log("✅ 微信连接测试成功")
                    messagebox.showinfo("测试结果", "微信连接测试成功！")
                else:
                    self.log("❌ 微信连接测试失败")
                    messagebox.showerror("测试结果", "微信连接测试失败！请检查配置是否正确")

            except Exception as e:
                self.log(f"❌ 微信连接测试失败: {e}")
                messagebox.showerror("测试结果", f"微信连接测试失败: {e}")

        threading.Thread(target=test_thread, daemon=True).start()

    def start_timer(self):
        """启动定时任务"""
        if self.is_running:
            return

        # 验证配置
        if not self.wechat_contact.get():
            messagebox.showerror("错误", "请先设置联系人名称")
            return

        if self.wechat_send_mode.get() == "webhook" and not self.wechat_webhook_url.get():
            messagebox.showerror("错误", "企业微信机器人模式需要设置webhook地址")
            return

        if self.timer_mode.get() == "daily" and not self.daily_times:
            messagebox.showerror("错误", "每日定时模式需要至少设置一个执行时间")
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        mode_text = "间隔执行" if self.timer_mode.get() == "interval" else "每日定时"
        self.status_label.config(text="状态: 运行中", foreground="green")
        self.mode_label.config(text=f"执行模式: {mode_text}")

        if self.timer_mode.get() == "interval":
            self.log(f"定时任务已启动 - {mode_text} (每{self.interval_minutes.get()}分钟)")
        else:
            times_str = ", ".join(self.daily_times)
            self.log(f"定时任务已启动 - {mode_text} ({times_str})")

        # 启动定时器线程
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()

    def stop_timer(self):
        """停止定时任务"""
        if not self.is_running:
            return

        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

        self.status_label.config(text="状态: 已停止", foreground="red")
        self.mode_label.config(text="执行模式: --")
        self.next_run_label.config(text="下次运行: --")

        self.log("定时任务已停止")

    def timer_worker(self):
        """定时器工作线程"""
        if self.timer_mode.get() == "interval":
            self.interval_timer_worker()
        else:
            self.daily_timer_worker()

    def interval_timer_worker(self):
        """间隔执行模式的定时器"""
        while self.is_running:
            # 计算下次执行时间
            next_run = datetime.now() + timedelta(minutes=self.interval_minutes.get())
            self.next_run_label.config(text=f"下次运行: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

            # 等待指定间隔
            for _ in range(self.interval_minutes.get() * 60):
                if not self.is_running:
                    break
                time.sleep(1)

            if self.is_running:
                # 执行任务
                self.execute_jd_cancel_task()

    def daily_timer_worker(self):
        """每日定时执行模式的定时器"""
        while self.is_running:
            now = datetime.now()
            next_run_time = self.get_next_daily_run_time()

            if next_run_time:
                self.next_run_label.config(text=f"下次运行: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 计算等待时间
                wait_seconds = (next_run_time - now).total_seconds()

                if wait_seconds > 0:
                    # 等待到执行时间
                    for _ in range(int(wait_seconds)):
                        if not self.is_running:
                            break
                        time.sleep(1)

                        # 每分钟更新一次显示
                        if _ % 60 == 0:
                            remaining = wait_seconds - _
                            if remaining > 3600:
                                self.log(f"距离下次执行还有 {remaining/3600:.1f} 小时")
                            elif remaining > 60:
                                self.log(f"距离下次执行还有 {remaining/60:.0f} 分钟")

                if self.is_running:
                    # 执行任务
                    self.execute_jd_cancel_task()
            else:
                # 如果没有设置时间，等待1分钟后重新检查
                time.sleep(60)

    def get_next_daily_run_time(self):
        """获取下次每日执行时间"""
        if not self.daily_times:
            return None

        now = datetime.now()
        today = now.date()

        # 检查今天是否还有未执行的时间
        for time_str in self.daily_times:
            hour, minute = map(int, time_str.split(':'))
            run_time = datetime.combine(today, datetime.min.time().replace(hour=hour, minute=minute))

            if run_time > now:
                return run_time

        # 如果今天没有了，返回明天的第一个时间
        tomorrow = today + timedelta(days=1)
        first_time = self.daily_times[0]
        hour, minute = map(int, first_time.split(':'))
        return datetime.combine(tomorrow, datetime.min.time().replace(hour=hour, minute=minute))

    def manual_execute(self):
        """手动执行一次"""
        self.log("开始手动执行京东取消订单处理...")

        def execute_thread():
            self.execute_jd_cancel_task()

        threading.Thread(target=execute_thread, daemon=True).start()

    def execute_jd_cancel_task(self):
        """执行京东取消订单任务"""
        try:
            self.log("开始处理京东取消订单...")

            # 初始化管理器
            if not self.manager:
                if self.wechat_send_mode.get() == "local":
                    self.manager = JDCancelManagerV2(
                        wechat_contact=self.wechat_contact.get(),
                        use_local_wechat=True
                    )
                else:
                    self.manager = JDCancelManagerV2(
                        wechat_contact=self.wechat_contact.get(),
                        use_local_wechat=False,
                        webhook_url=self.wechat_webhook_url.get()
                    )

            # 处理当天的京东取消订单
            result = self.manager.process_today_jd_cancellations()

            # 更新状态显示
            self.last_run_label.config(text=f"上次运行: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            if not result.get('success', True):
                self.result_label.config(text=f"上次结果: 执行失败 - {result.get('error', '未知错误')}")
                self.log(f"❌ 执行失败: {result.get('error', '未知错误')}")
            else:
                total_stockouts = result.get('total_stockouts', 0)
                canceled_stockouts = result.get('canceled_stockouts', 0)
                jd_count = result.get('jd_logistics_count', 0)
                sent = result.get('notification_sent', False)

                result_text = f"出库单{total_stockouts}个，取消{canceled_stockouts}个，京东{jd_count}个，通知{'已发送' if sent else '发送失败'}"
                self.result_label.config(text=f"上次结果: {result_text}")

                self.log(f"✅ 执行完成: {result_text}")

                # 显示京东物流单号
                if result.get('jd_logistics_numbers'):
                    self.log("京东物流单号:")
                    for number in result['jd_logistics_numbers']:
                        self.log(f"  {number}")
                else:
                    self.log("今天没有京东取消订单")

        except Exception as e:
            self.log(f"❌ 执行京东取消订单任务失败: {e}")
            self.result_label.config(text=f"上次结果: 执行异常 - {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = JDCancelGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
