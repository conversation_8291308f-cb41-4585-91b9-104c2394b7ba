# 京东取消订单微信通知工具使用说明

## 📋 功能概述

本工具可以自动获取当天已取消的销售订单中的京东物流单号，并通过企业微信机器人发送通知消息。

### 主要功能
- ✅ 自动查询当天已取消的出库单
- ✅ 提取京东物流单号（JD开头的物流单号）
- ✅ 通过企业微信机器人发送格式化通知
- ✅ 支持定时自动执行（间隔执行或每日定时）
- ✅ 可视化界面操作
- ✅ 配置自动保存

## 🚀 快速开始

### 1. 启动程序
```bash
python jd_cancel_gui.py
```

### 2. 配置微信机器人
1. **获取企业微信机器人webhook地址**
   - 在企业微信群中添加机器人
   - 复制webhook地址（格式：`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx`）

2. **填写配置信息**
   - **机器人Webhook URL**: 粘贴完整的webhook地址
   - **@联系人**: 填写要@的人员手机号，或填写`@all`@所有人

3. **测试连接**
   - 点击"测试微信连接"按钮验证配置是否正确

### 3. 设置定时任务
选择执行模式：

#### 间隔执行模式
- 设置间隔时间（分钟）
- 程序会按设定间隔重复执行

#### 每日定时模式（推荐）
- 设置具体的执行时间（时:分）
- 可以添加多个执行时间点
- 例如：09:00、14:00、18:00

### 4. 启动定时任务
- 点击"开始定时任务"启动自动执行
- 或点击"立即执行一次"进行手动测试

## 📱 微信通知格式

当检测到已取消的京东订单时，会发送如下格式的消息：

```
物流单号
JDAZ20474031170
JDAZ20473059809
JDAZ20472646572

以上京东单号订单取消，实物未发出共计3单，请处理！
```

## ⚙️ 配置说明

### 微信机器人配置
| 配置项 | 说明 | 示例 |
|--------|------|------|
| 机器人Webhook URL | 企业微信机器人的完整webhook地址 | `https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx` |
| @联系人 | 要@的联系人手机号或@all | `13800138000` 或 `@all` |

### 定时设置
| 模式 | 说明 | 适用场景 |
|------|------|----------|
| 间隔执行 | 按固定间隔重复执行 | 需要频繁检查的场景 |
| 每日定时 | 在指定时间点执行 | 定期检查，如每天上午、下午各一次 |

### 自动启动
- 勾选"程序启动时自动开始定时任务"
- 程序启动后会自动开始定时任务，无需手动点击

## 🔧 使用流程

### 首次使用
1. **启动程序**: 运行 `python jd_cancel_gui.py`
2. **配置微信**: 设置机器人webhook地址和联系人
3. **测试连接**: 验证微信配置是否正确
4. **设置定时**: 选择合适的执行模式和时间
5. **手动测试**: 点击"立即执行一次"测试完整流程
6. **启动定时**: 点击"开始定时任务"

### 日常使用
1. **自动运行**: 如果开启了自动启动，程序会自动开始定时任务
2. **监控状态**: 通过状态区域查看运行情况
3. **查看日志**: 通过日志区域了解详细执行过程
4. **手动干预**: 需要时可以手动执行或停止任务

## 📊 状态说明

### 运行状态
- **未启动**: 定时任务未开始
- **运行中**: 定时任务正在运行
- **已停止**: 定时任务已停止

### 执行结果
- **取消订单X个**: 查询到的已取消出库单数量
- **京东X个**: 提取到的京东物流单号数量
- **通知已发送/发送失败**: 微信通知发送状态

## 🔍 故障排除

### 常见问题

#### 1. 微信连接测试失败
**可能原因**:
- webhook地址不正确
- 网络连接问题
- 机器人被禁用

**解决方法**:
- 检查webhook地址是否完整正确
- 确认网络连接正常
- 在企业微信中检查机器人状态

#### 2. 没有查询到取消订单
**可能原因**:
- 当天确实没有取消的订单
- API权限问题
- 时间范围设置问题

**解决方法**:
- 确认当天是否有取消的订单
- 检查API配置是否正确
- 查看日志了解详细错误信息

#### 3. 程序无法启动
**可能原因**:
- 缺少依赖库
- Python环境问题
- 配置文件损坏

**解决方法**:
```bash
# 安装依赖
pip install requests openpyxl python-dotenv

# 删除配置文件重新配置
rm jd_cancel_config.json
```

## 📝 日志说明

程序会在界面下方显示详细的运行日志，包括：
- 程序启动信息
- 配置加载状态
- API查询结果
- 微信发送状态
- 错误信息

## ⚠️ 注意事项

1. **API权限**: 确保账号具有出库单查询权限
2. **网络连接**: 需要能够访问旺店通API和企业微信API
3. **微信机器人**: 确保机器人在目标群中且未被禁用
4. **时间设置**: 建议设置合理的执行频率，避免过于频繁
5. **数据准确性**: 程序只处理状态为"已取消"(5)的出库单

## 🔄 更新日志

### v1.0.0 (2025-07-16)
- ✅ 初始版本发布
- ✅ 支持京东取消订单查询
- ✅ 企业微信机器人通知
- ✅ 可视化界面操作
- ✅ 定时任务功能
- ✅ 配置自动保存

## 📞 技术支持

如遇到问题，请检查：
1. 日志输出中的错误信息
2. 网络连接状态
3. API配置是否正确
4. 微信机器人配置是否有效
