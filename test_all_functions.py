#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试所有功能
"""

import logging
import time
from simplified_wechat_sender import SimplifiedWeChatSender
from jd_cancel_manager_v2 import JDCancelManagerV2
from stockout_details_manager import StockoutDetailsManager

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_simplified_wechat_sender():
    """测试简化微信发送器"""
    print("=" * 60)
    print("🧪 测试简化微信发送器")
    print("=" * 60)
    
    sender = SimplifiedWeChatSender(contact_name="文件传输助手")
    
    # 测试可用性
    availability = sender.test_wechat_availability()
    print("微信发送可用性测试:")
    for method, available in availability.items():
        if method != 'details':
            status = "✅ 可用" if available else "❌ 不可用"
            print(f"  {method}: {status}")
    
    print("\n详细信息:")
    for method, detail in availability['details'].items():
        print(f"  {method}: {detail}")
    
    # 测试发送
    test_message = f"简化微信发送器测试 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
    print(f"\n测试发送消息: {test_message}")
    
    if sender.send_text_message(test_message):
        print("✅ 消息发送成功")
        return True
    else:
        print("❌ 消息发送失败")
        return False

def test_stockout_details_manager():
    """测试出库单详情管理器"""
    print("\n" + "=" * 60)
    print("🧪 测试出库单详情管理器")
    print("=" * 60)
    
    manager = StockoutDetailsManager()
    
    # 测试获取已取消的京东物流单号
    result = manager.get_canceled_jd_logistics_today()
    
    print(f"处理结果:")
    print(f"  成功: {result['success']}")
    print(f"  已取消订单数: {result['total_canceled_orders']}")
    print(f"  京东订单数: {result['jd_canceled_orders']}")
    print(f"  京东物流单号数: {result['jd_logistics_count']}")
    
    if result['jd_logistics_numbers']:
        print(f"  京东物流单号: {result['jd_logistics_numbers']}")
    else:
        print("  ✅ 今天没有已取消的京东物流单号")
    
    return result['success']

def test_jd_cancel_manager():
    """测试京东取消订单管理器"""
    print("\n" + "=" * 60)
    print("🧪 测试京东取消订单管理器")
    print("=" * 60)
    
    manager = JDCancelManagerV2(
        wechat_contact="文件传输助手",
        use_local_wechat=True
    )
    
    # 测试微信连接
    print("测试微信连接...")
    if manager.test_wechat_connection():
        print("✅ 微信连接测试成功")
    else:
        print("❌ 微信连接测试失败")
    
    # 测试处理今天的京东取消订单
    print("\n处理今天的京东取消订单...")
    result = manager.process_today_jd_cancellations()
    
    print(f"处理结果:")
    print(f"  成功: {result['success']}")
    print(f"  已取消订单数: {result['total_canceled_orders']}")
    print(f"  京东订单数: {result['jd_canceled_orders']}")
    print(f"  京东物流单号数: {result['jd_logistics_count']}")
    print(f"  通知发送: {result['notification_sent']}")
    
    return result['success']

def test_integration():
    """集成测试"""
    print("\n" + "=" * 60)
    print("🧪 集成测试")
    print("=" * 60)
    
    # 模拟有京东物流单号的情况
    sender = SimplifiedWeChatSender(contact_name="文件传输助手")
    
    # 测试发送京东取消订单通知
    test_jd_numbers = ["JDAZ20504424522", "JDAZ20504424513"]
    
    print(f"测试发送京东取消订单通知...")
    print(f"测试物流单号: {test_jd_numbers}")
    
    if sender.send_jd_cancel_orders(test_jd_numbers):
        print("✅ 京东取消订单通知发送成功")
        return True
    else:
        print("❌ 京东取消订单通知发送失败")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    print("🚀 开始测试所有功能...")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试1: 简化微信发送器
    try:
        result1 = test_simplified_wechat_sender()
        results.append(("简化微信发送器", result1))
    except Exception as e:
        print(f"❌ 简化微信发送器测试失败: {e}")
        results.append(("简化微信发送器", False))
    
    # 测试2: 出库单详情管理器
    try:
        result2 = test_stockout_details_manager()
        results.append(("出库单详情管理器", result2))
    except Exception as e:
        print(f"❌ 出库单详情管理器测试失败: {e}")
        results.append(("出库单详情管理器", False))
    
    # 测试3: 京东取消订单管理器
    try:
        result3 = test_jd_cancel_manager()
        results.append(("京东取消订单管理器", result3))
    except Exception as e:
        print(f"❌ 京东取消订单管理器测试失败: {e}")
        results.append(("京东取消订单管理器", False))
    
    # 测试4: 集成测试
    try:
        result4 = test_integration()
        results.append(("集成测试", result4))
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        results.append(("集成测试", False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return success_count == len(results)

if __name__ == '__main__':
    main()
