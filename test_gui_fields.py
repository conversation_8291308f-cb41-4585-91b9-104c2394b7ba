#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试GUI字段映射
"""

import logging
from jd_cancel_manager_v2 import JDCancelManagerV2

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_gui_field_mapping():
    """测试GUI字段映射"""
    print("🧪 测试GUI字段映射")
    print("=" * 50)
    
    # 创建管理器
    manager = JDCancelManagerV2(
        wechat_contact="文件传输助手",
        use_local_wechat=True
    )
    
    # 调用处理方法
    result = manager.process_today_jd_cancellations()
    
    print("返回结果字段:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    print("\nGUI需要的字段检查:")
    
    # 检查GUI需要的字段
    required_fields = [
        'success',
        'total_stockouts',      # GUI显示：出库单总数
        'canceled_stockouts',   # GUI显示：已取消出库单数
        'jd_logistics_count',   # GUI显示：京东物流单号数
        'notification_sent',    # GUI显示：通知发送状态
        'jd_logistics_numbers'  # GUI显示：京东物流单号列表
    ]
    
    missing_fields = []
    for field in required_fields:
        if field in result:
            print(f"  ✅ {field}: {result[field]}")
        else:
            print(f"  ❌ {field}: 缺失")
            missing_fields.append(field)
    
    if missing_fields:
        print(f"\n❌ 缺失字段: {missing_fields}")
        return False
    else:
        print(f"\n✅ 所有GUI需要的字段都存在")
        return True

def simulate_gui_display(result):
    """模拟GUI显示逻辑"""
    print("\n🖥️ 模拟GUI显示逻辑")
    print("=" * 50)
    
    if not result.get('success', True):
        print(f"❌ 执行失败: {result.get('error', '未知错误')}")
        return
    
    total_stockouts = result.get('total_stockouts', 0)
    canceled_stockouts = result.get('canceled_stockouts', 0)
    jd_count = result.get('jd_logistics_count', 0)
    sent = result.get('notification_sent', False)
    
    result_text = f"出库单{total_stockouts}个，取消{canceled_stockouts}个，京东{jd_count}个，通知{'已发送' if sent else '发送失败'}"
    
    print(f"GUI显示结果: {result_text}")
    
    # 显示京东物流单号
    if result.get('jd_logistics_numbers'):
        print("京东物流单号:")
        for number in result['jd_logistics_numbers']:
            print(f"  {number}")
    else:
        print("今天没有京东取消订单")

def main():
    """主函数"""
    logger = setup_logging()
    
    print("🚀 开始测试GUI字段映射...")
    
    # 测试字段映射
    result = None
    try:
        manager = JDCancelManagerV2(
            wechat_contact="文件传输助手",
            use_local_wechat=True
        )
        
        result = manager.process_today_jd_cancellations()
        
        if test_gui_field_mapping():
            print("✅ 字段映射测试通过")
        else:
            print("❌ 字段映射测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    # 模拟GUI显示
    if result:
        simulate_gui_display(result)
    
    print("\n🎉 测试完成")
    return True

if __name__ == '__main__':
    main()
