#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天已取消的销售出库单
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_time_range():
    """获取当天的时间范围"""
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now  # 使用当前时间作为结束时间
    return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')

def query_canceled_sales(client, start_time, end_time):
    """查询已取消的销售出库单"""
    logger = logging.getLogger(__name__)
    canceled_status = 5  # 已取消状态码
    page_size = 100  # 每页大小
    page_no = 0  # 从第0页开始
    total_canceled = 0
    
    logger.info(f"开始查询{start_time}至{end_time}期间已取消的销售出库单...")
    logger.debug(f"查询参数: status={canceled_status}, page_size={page_size}")
    
    while True:
        try:
            logger.debug(f"正在查询第{page_no + 1}页...")
            result = client.query_sales_trades(
                start_time=start_time,
                end_time=end_time,
                trade_status=canceled_status,
                page_no=page_no,
                page_size=page_size
            )
            
            # 调试日志：打印完整响应
            logger.debug(f"API响应: {result}")
            
            # 检查响应结构
            if not isinstance(result, dict):
                logger.error(f"无效的API响应格式: {type(result)}")
                break
                
            # 处理查询结果
            trades = result.get('trades', [])
            count = len(trades)
            total_canceled += count
            
            if count > 0:
                logger.info(f"第{page_no + 1}页查询到{count}条已取消订单:")
                for trade in trades:
                    logger.info(f"订单号: {trade.get('trade_no')}, 取消时间: {trade.get('modified')}")
            else:
                logger.debug("当前页没有查询到已取消订单")
            
            # 检查是否还有更多数据
            if count < page_size:
                logger.debug("已到达最后一页数据")
                break
                
            page_no += 1
            
        except Exception as e:
            logger.error(f"查询失败: {str(e)}", exc_info=True)
            break
    
    if total_canceled == 0:
        logger.warning(f"在{start_time}至{end_time}期间没有查询到任何已取消的销售出库单")
    else:
        logger.info(f"共查询到{total_canceled}条已取消的销售出库单")
    return total_canceled

def test_api_permission(client):
    """测试API权限"""
    logger = logging.getLogger(__name__)
    try:
        # 测试连接
        if not client.test_connection():
            logger.error("API连接测试失败，请检查网络和配置")
            return False
        
        # 测试查询权限
        test_params = {
            'start_time': '2023-01-01 00:00:00',
            'end_time': '2023-01-01 23:59:59',
            'trade_status': 5,
            'page_size': 1
        }
        result = client.query_sales_trades(**test_params)
        
        if 'code' in result and result['code'] != '0':
            logger.error(f"API权限不足: {result.get('message', '未知错误')}")
            logger.error("请确认账号是否有查询已取消订单的权限")
            return False
            
        return True
    except Exception as e:
        logger.error(f"权限验证失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 验证API权限
        if not test_api_permission(client):
            logger.error("程序终止: API权限验证失败")
            return 1
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询已取消的销售出库单
        query_canceled_sales(client, start_time, end_time)
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == '__main__':
    main()
