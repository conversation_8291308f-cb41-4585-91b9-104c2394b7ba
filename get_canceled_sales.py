#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取当天已取消的销售出库单
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
import os

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_today_time_range():
    """获取当天的时间范围"""
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now  # 使用当前时间作为结束时间
    return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')

def query_canceled_sales(client, start_time, end_time):
    """查询已取消的销售出库单"""
    logger = logging.getLogger(__name__)
    canceled_status = 5  # 已取消状态码
    page_size = 100  # 每页大小
    page_no = 0  # 从第0页开始
    total_canceled = 0
    all_trades = []  # 存储所有查询到的订单

    logger.info(f"开始查询{start_time}至{end_time}期间已取消的销售出库单...")
    logger.debug(f"查询参数: status={canceled_status}, page_size={page_size}")
    
    while True:
        try:
            logger.debug(f"正在查询第{page_no + 1}页...")
            result = client.query_sales_trades(
                start_time=start_time,
                end_time=end_time,
                trade_status=canceled_status,
                page_no=page_no,
                page_size=page_size
            )
            
            # 调试日志：打印完整响应
            logger.info(f"API响应结构: {list(result.keys()) if isinstance(result, dict) else type(result)}")
            logger.debug(f"API完整响应: {result}")

            # 检查响应结构
            if not isinstance(result, dict):
                logger.error(f"无效的API响应格式: {type(result)}")
                break
                
            # 处理查询结果 - 根据实际响应结构解析
            trades = []

            # 根据实际API响应结构解析数据
            if 'content' in result:
                content = result.get('content', [])
                if isinstance(content, list):
                    trades = content
                elif isinstance(content, dict) and 'trades' in content:
                    trades = content.get('trades', [])
            elif 'trades' in result:
                trades = result.get('trades', [])
            elif 'data' in result:
                data = result.get('data', [])
                if isinstance(data, list):
                    trades = data
                elif isinstance(data, dict) and 'trades' in data:
                    trades = data.get('trades', [])

            count = len(trades)
            total_canceled += count

            # 获取总记录数（仅第一页时显示）
            if page_no == 0:
                total_records = result.get('total', 0)
                logger.info(f"API返回总记录数: {total_records}")

            if count > 0:
                # 将当前页的订单添加到总列表
                all_trades.extend(trades)

                logger.info(f"第{page_no + 1}页查询到{count}条已取消订单:")
                for i, trade in enumerate(trades, 1):
                    trade_no = trade.get('trade_no', trade.get('tid', '未知'))

                    # 尝试获取各种时间字段
                    time_fields = ['modified', 'update_time', 'cancel_time', 'created', 'trade_time']
                    cancel_time = '未知'
                    for field in time_fields:
                        if trade.get(field):
                            cancel_time = trade.get(field)
                            break

                    # 获取其他有用信息
                    buyer_nick = trade.get('buyer_nick', trade.get('buyer_name', ''))
                    shop_name = trade.get('shop_name', '')
                    total_amount = trade.get('total_amount', trade.get('payment', ''))

                    logger.info(f"  {i}. 订单号: {trade_no}")
                    logger.info(f"     时间: {cancel_time}")
                    if buyer_nick:
                        logger.info(f"     买家: {buyer_nick}")
                    if shop_name:
                        logger.info(f"     店铺: {shop_name}")
                    if total_amount:
                        logger.info(f"     金额: {total_amount}")
                    logger.info("")  # 空行分隔
            else:
                logger.debug("当前页没有查询到已取消订单")
            
            # 检查是否还有更多数据
            if count < page_size:
                logger.debug("已到达最后一页数据")
                break
                
            page_no += 1
            
        except Exception as e:
            logger.error(f"查询失败: {str(e)}", exc_info=True)
            break
    
    if total_canceled == 0:
        logger.warning(f"在{start_time}至{end_time}期间没有查询到任何已取消的销售出库单")
    else:
        logger.info(f"共查询到{total_canceled}条已取消的销售出库单")

    return all_trades

def test_api_permission(client):
    """测试API权限"""
    logger = logging.getLogger(__name__)
    try:
        # 测试连接
        if not client.test_connection():
            logger.error("API连接测试失败，请检查网络和配置")
            return False

        # 测试查询权限 - 使用较近的日期进行测试
        from datetime import datetime, timedelta
        test_end = datetime.now()
        test_start = test_end - timedelta(days=1)

        test_params = {
            'start_time': test_start.strftime('%Y-%m-%d %H:%M:%S'),
            'end_time': test_end.strftime('%Y-%m-%d %H:%M:%S'),
            'trade_status': 5,
            'page_size': 1
        }

        logger.info(f"测试API权限，查询参数: {test_params}")
        result = client.query_sales_trades(**test_params)

        # 检查API响应格式
        if not isinstance(result, dict):
            logger.error(f"API响应格式异常: {type(result)}")
            return False

        # 旺店通API成功响应应该包含flag字段
        if result.get('flag') != 'success':
            logger.error(f"API调用失败: {result.get('message', '未知错误')}")
            logger.error("请确认账号是否有查询已取消订单的权限")
            return False

        logger.info("API权限验证成功")
        return True

    except Exception as e:
        logger.error(f"权限验证失败: {e}")
        return False

def export_to_excel(trades, filename=None):
    """导出已取消订单到Excel文件"""
    logger = logging.getLogger(__name__)

    if not trades:
        logger.warning("没有数据可导出")
        return False

    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment, PatternFill

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "已取消销售订单"

        # 设置表头
        headers = [
            '序号', '订单号', '原始订单号', '买家昵称', '店铺名称',
            '订单金额', '创建时间', '修改时间', '取消时间', '状态',
            '收件人', '收件人电话', '收件人地址', '备注'
        ]

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 写入数据
        for row, trade in enumerate(trades, 2):
            # 尝试获取各种时间字段
            time_fields = ['modified', 'update_time', 'cancel_time']
            cancel_time = ''
            for field in time_fields:
                if trade.get(field):
                    cancel_time = trade.get(field)
                    break

            data = [
                row - 1,  # 序号
                trade.get('trade_no', trade.get('tid', '')),
                trade.get('src_tid', trade.get('original_tid', '')),
                trade.get('buyer_nick', trade.get('buyer_name', '')),
                trade.get('shop_name', ''),
                trade.get('total_amount', trade.get('payment', '')),
                trade.get('created', ''),
                trade.get('modified', ''),
                cancel_time,
                '已取消',
                trade.get('receiver_name', ''),
                trade.get('receiver_mobile', trade.get('receiver_phone', '')),
                f"{trade.get('receiver_province', '')}{trade.get('receiver_city', '')}{trade.get('receiver_district', '')}{trade.get('receiver_address', '')}",
                trade.get('remark', trade.get('buyer_message', ''))
            ]

            for col, value in enumerate(data, 1):
                ws.cell(row=row, column=col, value=value)

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 生成文件名
        if not filename:
            today = datetime.now().strftime('%Y%m%d')
            filename = f"已取消销售订单_{today}.xlsx"

        # 保存文件
        wb.save(filename)
        logger.info(f"✅ 已导出 {len(trades)} 条记录到文件: {filename}")
        return True

    except ImportError:
        logger.error("请安装openpyxl库: pip install openpyxl")
        return False
    except Exception as e:
        logger.error(f"导出Excel失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        # 初始化API客户端
        client = WDTPostClient()
        
        # 验证API权限
        if not test_api_permission(client):
            logger.error("程序终止: API权限验证失败")
            return 1
        
        # 获取当天时间范围
        start_time, end_time = get_today_time_range()
        logger.info(f"查询时间范围: {start_time} 至 {end_time}")
        
        # 查询已取消的销售出库单
        query_canceled_sales(client, start_time, end_time)
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == '__main__':
    main()
