#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试物流单号获取和微信发送问题
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
from local_wechat_sender import LocalWeChatSender, WeChatWindowFinder
import json

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_logistics_numbers():
    """调试物流单号获取问题"""
    logger = setup_logging()
    client = WDTPostClient()
    
    logger.info("=== 调试物流单号获取问题 ===")
    
    # 获取当天时间范围
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"查询时间范围: {start_str} 至 {end_str}")
    
    try:
        # 查询已取消的销售订单
        params = {
            "start_time": start_str,
            "end_time": end_str,
            "trade_status": 5,  # 已取消
            "page_size": 20,
            "page_no": 0
        }
        
        logger.info(f"查询参数: {json.dumps(params, ensure_ascii=False, indent=2)}")
        result = client.query_sales_trades(**params)
        
        if result and result.get('flag') == 'success':
            content = result.get('content', [])
            logger.info(f"查询到 {len(content)} 条已取消订单")
            
            # 详细分析每个订单的物流信息
            for i, order in enumerate(content):
                logger.info(f"\n--- 订单 {i+1} 详细信息 ---")
                logger.info(f"订单号: {order.get('trade_no', '未知')}")
                logger.info(f"平台: {order.get('platform_name', '未知')}")
                logger.info(f"状态: {order.get('trade_status', '未知')}")
                logger.info(f"物流编码: {order.get('logistics_code', '未知')}")
                
                # 检查所有可能的物流单号字段
                logistics_fields = [
                    'logistics_no', 'express_no', 'tracking_no', 'waybill_no',
                    'express_number', 'delivery_no', 'shipping_no'
                ]
                
                logger.info("物流单号字段检查:")
                has_logistics = False
                for field in logistics_fields:
                    value = order.get(field, '')
                    if value:
                        logger.info(f"  {field}: {value}")
                        has_logistics = True
                    else:
                        logger.debug(f"  {field}: (空)")
                
                if not has_logistics:
                    logger.warning("  ⚠️ 该订单没有任何物流单号字段有值")
                
                # 检查是否为京东物流
                logistics_code = order.get('logistics_code', '')
                if logistics_code and str(logistics_code).upper() in ['JBD', 'JD', 'JINGDONG']:
                    logger.info(f"  ✅ 这是京东物流订单 (编码: {logistics_code})")
                else:
                    logger.info(f"  ❌ 这不是京东物流订单 (编码: {logistics_code})")
                
                # 显示订单的所有字段（用于发现可能的物流单号字段）
                logger.debug(f"订单所有字段: {list(order.keys())}")
        
        else:
            logger.error("查询销售订单失败")
            if result:
                logger.error(f"错误信息: {result.get('message', '未知错误')}")
    
    except Exception as e:
        logger.error(f"调试物流单号获取失败: {e}")

def debug_wechat_sending():
    """调试微信发送问题"""
    logger = setup_logging()
    
    logger.info("\n=== 调试微信发送问题 ===")
    
    # 1. 检查微信窗口
    logger.info("1. 检查微信窗口...")
    windows = WeChatWindowFinder.get_all_wechat_windows()
    
    if windows:
        logger.info(f"找到 {len(windows)} 个微信窗口:")
        for i, window in enumerate(windows):
            logger.info(f"  窗口 {i+1}:")
            logger.info(f"    标题: {window['title']}")
            logger.info(f"    类名: {window['class_name']}")
            logger.info(f"    句柄: {window['hwnd']}")
            logger.info(f"    位置: {window['rect']}")
    else:
        logger.error("❌ 未找到微信窗口，请确保微信已启动并登录")
        return
    
    # 2. 测试微信发送
    logger.info("\n2. 测试微信发送...")
    
    # 获取用户输入的联系人
    contact_name = input("请输入要测试的联系人名称 (建议使用'文件传输助手'): ").strip()
    
    if not contact_name:
        logger.warning("未输入联系人名称，使用默认值: 文件传输助手")
        contact_name = "文件传输助手"
    
    try:
        sender = LocalWeChatSender(contact_name)
        
        # 测试消息
        test_message = f"微信发送测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        logger.info(f"尝试发送测试消息给: {contact_name}")
        logger.info(f"测试消息内容: {test_message}")
        
        # 分步测试
        logger.info("\n--- 分步测试微信发送 ---")
        
        # 步骤1: 查找微信窗口
        logger.info("步骤1: 查找微信窗口...")
        hwnd = sender.find_wechat_window()
        if hwnd:
            logger.info(f"✅ 找到微信窗口: {hwnd}")
        else:
            logger.error("❌ 未找到微信窗口")
            return
        
        # 步骤2: 激活微信窗口
        logger.info("步骤2: 激活微信窗口...")
        if sender.activate_wechat_window(hwnd):
            logger.info("✅ 微信窗口激活成功")
        else:
            logger.error("❌ 微信窗口激活失败")
            return
        
        # 步骤3: 搜索联系人
        logger.info(f"步骤3: 搜索联系人 '{contact_name}'...")
        if sender.search_contact(contact_name):
            logger.info("✅ 联系人搜索成功")
        else:
            logger.error("❌ 联系人搜索失败")
            return
        
        # 步骤4: 发送消息
        logger.info("步骤4: 发送消息...")
        if sender.send_message_to_clipboard(test_message):
            logger.info("✅ 消息发送成功")
        else:
            logger.error("❌ 消息发送失败")
            return
        
        logger.info("🎉 微信发送测试完全成功！")
        
    except Exception as e:
        logger.error(f"微信发送测试异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

def debug_combined_process():
    """调试完整流程"""
    logger = setup_logging()
    
    logger.info("\n=== 调试完整流程 ===")
    
    try:
        from stockout_details_manager import StockoutDetailsManager
        
        # 创建管理器
        manager = StockoutDetailsManager()
        
        # 获取已取消的京东物流单号
        logger.info("获取已取消的京东物流单号...")
        result = manager.get_canceled_jd_logistics_today()
        
        if result['success']:
            logger.info(f"✅ 成功获取数据:")
            logger.info(f"  已取消订单数: {result['total_canceled_orders']}")
            logger.info(f"  京东物流单号数: {result['jd_logistics_count']}")
            
            if result['jd_logistics_numbers']:
                logger.info("  京东物流单号:")
                for number in result['jd_logistics_numbers']:
                    logger.info(f"    {number}")
                
                # 测试微信发送
                contact_name = input("\n请输入微信联系人名称进行发送测试: ").strip()
                if contact_name:
                    from jd_cancel_manager_v2 import JDCancelManagerV2
                    
                    test_manager = JDCancelManagerV2(
                        wechat_contact=contact_name,
                        use_local_wechat=True
                    )
                    
                    logger.info("测试微信发送...")
                    if test_manager.send_jd_cancel_notification(result['jd_logistics_numbers']):
                        logger.info("✅ 微信发送测试成功")
                    else:
                        logger.error("❌ 微信发送测试失败")
            else:
                logger.info("  今天没有京东取消订单")
        else:
            logger.error(f"❌ 获取数据失败: {result.get('error', '未知错误')}")
    
    except Exception as e:
        logger.error(f"完整流程测试失败: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

if __name__ == '__main__':
    print("🔍 开始调试物流单号获取和微信发送问题...")
    print("\n请选择调试模式:")
    print("1. 调试物流单号获取")
    print("2. 调试微信发送")
    print("3. 调试完整流程")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        debug_logistics_numbers()
    elif choice == "2":
        debug_wechat_sending()
    elif choice == "3":
        debug_combined_process()
    else:
        print("无效选择，退出程序")
    
    print("\n🎯 调试完成")
