#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
京东取消订单管理器 V2
支持本地微信发送和优化的出库明细查询
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from stockout_details_manager import StockoutDetailsManager
from enhanced_wechat_sender import EnhancedWeChatSender

class JDCancelManagerV2:
    """京东取消订单管理器 V2"""
    
    def __init__(self, 
                 wechat_contact: str = "",
                 use_local_wechat: bool = True,
                 webhook_url: str = ""):
        """
        初始化管理器
        
        Args:
            wechat_contact: 微信联系人名称
            use_local_wechat: 是否使用本地微信发送
            webhook_url: 企业微信机器人webhook地址（当use_local_wechat=False时使用）
        """
        self.wechat_contact = wechat_contact
        self.use_local_wechat = use_local_wechat
        self.stockout_manager = StockoutDetailsManager()
        
        # 初始化增强微信发送器
        self.wechat_sender = EnhancedWeChatSender(
            contact_name=wechat_contact if use_local_wechat else None,
            webhook_url=webhook_url if not use_local_wechat else None
        )
        
        self.logger = logging.getLogger(__name__)
        
    def get_canceled_jd_logistics_today(self) -> Dict[str, Any]:
        """
        获取当天已取消的京东物流单号
        
        Returns:
            处理结果字典
        """
        self.logger.info("开始获取当天已取消的京东物流单号...")
        return self.stockout_manager.get_canceled_jd_logistics_today()
    
    def send_jd_cancel_notification(self, jd_numbers: List[str]) -> bool:
        """
        发送京东取消订单通知
        
        Args:
            jd_numbers: 京东物流单号列表
            
        Returns:
            发送是否成功
        """
        if not jd_numbers:
            self.logger.info("没有京东物流单号需要发送")
            return True
        
        try:
            # 使用增强微信发送器（自动选择最佳发送方式）
            success = self.wechat_sender.send_jd_cancel_orders(jd_numbers, self.wechat_contact)
            
            if success:
                self.logger.info(f"成功发送{len(jd_numbers)}个京东取消订单通知")
            else:
                self.logger.error("发送京东取消订单通知失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"发送通知异常: {e}")
            return False
    
    def process_today_jd_cancellations(self) -> Dict[str, Any]:
        """
        处理当天的京东取消订单
        
        Returns:
            处理结果统计
        """
        try:
            # 1. 获取当天已取消的京东物流单号
            result = self.get_canceled_jd_logistics_today()
            
            if not result['success']:
                return result
            
            jd_numbers = result['jd_logistics_numbers']
            
            # 2. 发送微信通知
            send_success = False
            if jd_numbers:
                send_success = self.send_jd_cancel_notification(jd_numbers)
            
            # 3. 更新结果
            result.update({
                'notification_sent': send_success,
                'wechat_method': 'local' if self.use_local_wechat else 'webhook',
                'canceled_stockouts': result.get('total_stockouts', 0)  # 兼容性字段
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理京东取消订单失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_stockouts': 0,
                'canceled_stockouts': 0,
                'jd_logistics_numbers': [],
                'jd_logistics_count': 0,
                'notification_sent': False,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def test_wechat_connection(self) -> bool:
        """
        测试微信连接

        Returns:
            连接是否正常
        """
        try:
            availability = self.wechat_sender.test_wechat_availability()
            return (availability['local_wechat'] or
                   availability['enterprise_wechat'] or
                   availability['clipboard_method'])
        except Exception as e:
            self.logger.error(f"测试微信连接失败: {e}")
            return False
    
    def print_detailed_report(self, result: Dict[str, Any]):
        """
        打印详细处理报告
        
        Args:
            result: 处理结果字典
        """
        if not result['success']:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
            return
        
        print("=" * 60)
        print("📊 京东取消订单处理报告")
        print("=" * 60)
        print(f"📅 处理时间: {result['process_time']}")
        print(f"📦 已取消销售订单数: {result.get('total_canceled_orders', 0)}")
        print(f"🚚 京东物流单号数: {result['jd_logistics_count']}")
        print(f"📱 微信发送方式: {'本地微信' if result.get('wechat_method') == 'local' else '企业微信机器人'}")
        print(f"✅ 通知发送状态: {'成功' if result.get('notification_sent') else '失败'}")
        
        if result['jd_logistics_numbers']:
            print("\n📋 京东物流单号列表:")
            for i, number in enumerate(result['jd_logistics_numbers'], 1):
                print(f"  {i}. {number}")
            
            print(f"\n📨 发送的微信消息内容:")
            print("-" * 40)
            print("物流单号")
            for number in result['jd_logistics_numbers']:
                print(number)
            print(f"\n以上京东单号订单取消，实物未发出共计{len(result['jd_logistics_numbers'])}单，请处理！")
            print("-" * 40)
        else:
            print("\n✅ 今天没有已取消的京东订单")
        
        # 打印统计信息
        if 'canceled_statistics' in result:
            stats = result['canceled_statistics']
            print(f"\n📈 已取消订单统计:")

            # 状态统计
            if 'status_count' in stats:
                for status, count in stats['status_count'].items():
                    status_name = {
                        '5': '已取消',
                        '95': '已发货',
                        '30': '待审核'
                    }.get(status, f'状态{status}')
                    print(f"  {status_name}: {count}个")

            # 物流公司统计
            if 'logistics_count' in stats and stats['logistics_count']:
                print(f"\n🚛 物流公司统计:")
                for logistics_code, count in stats['logistics_count'].items():
                    logistics_name = {
                        'JBD': '京东快递',
                        'SF': '顺丰速运',
                        'YTO': '圆通速递',
                        'ZTO': '中通快递'
                    }.get(logistics_code, logistics_code)
                    print(f"  {logistics_name}({logistics_code}): {count}个")

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主函数 - 用于测试"""
    logger = setup_logging()
    
    print("=" * 60)
    print("🚀 京东取消订单管理器 V2 测试")
    print("=" * 60)
    
    # 选择微信发送方式
    print("请选择微信发送方式:")
    print("1. 本地微信发送（推荐）")
    print("2. 企业微信机器人")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 本地微信发送
        contact_name = input("请输入微信联系人名称: ").strip()
        if not contact_name:
            print("❌ 未输入联系人名称")
            return
        
        manager = JDCancelManagerV2(
            wechat_contact=contact_name,
            use_local_wechat=True
        )
        
    elif choice == "2":
        # 企业微信机器人
        webhook_url = input("请输入企业微信机器人webhook地址: ").strip()
        contact = input("请输入@联系人(手机号/@all): ").strip()
        
        if not webhook_url:
            print("❌ 未输入webhook地址")
            return
        
        manager = JDCancelManagerV2(
            wechat_contact=contact,
            use_local_wechat=False,
            webhook_url=webhook_url
        )
    else:
        print("❌ 无效选择")
        return
    
    try:
        # 测试微信连接
        print("\n🔍 测试微信连接...")
        if manager.test_wechat_connection():
            print("✅ 微信连接测试成功")
        else:
            print("❌ 微信连接测试失败")
            return
        
        # 处理当天的京东取消订单
        print("\n📦 开始处理当天的京东取消订单...")
        result = manager.process_today_jd_cancellations()
        
        # 打印详细报告
        print("\n")
        manager.print_detailed_report(result)
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == '__main__':
    main()
