#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
销售出库明细管理模块
专门用于获取已取消的销售出库明细并提取京东物流单号
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class StockoutDetailsManager:
    """销售出库明细管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.client = WDTPostClient()
        self.logger = logging.getLogger(__name__)
        
    def get_all_stockouts_today(self) -> List[Dict[str, Any]]:
        """
        获取当天所有的销售出库明细
        
        Returns:
            所有出库明细列表
        """
        # 获取当天时间范围
        now = datetime.now()
        start_time = datetime.combine(now.date(), datetime.min.time())
        end_time = now
        
        return self.get_all_stockouts(
            start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time.strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def get_all_stockouts(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内所有的销售出库明细
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            
        Returns:
            所有出库明细列表
        """
        self.logger.info(f"开始查询{start_time}至{end_time}期间所有销售出库明细...")
        
        page_size = 100
        page_no = 0
        all_stockouts = []
        max_pages = 1000  # 增加最大页数限制，支持更多数据

        # 构建查询参数
        base_params = {
            "start_consign_time": start_time,
            "end_consign_time": end_time,
            "page_size": page_size
        }
        
        while True:
            try:
                self.logger.debug(f"正在查询第{page_no + 1}页...")
                
                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }
                
                # 调用出库单查询API
                result = self.client.call_api('stockout.query', current_params)
                
                if not result:
                    break
                
                # 获取数据
                content = result.get('content', [])
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', []) or content.get('stockouts', [])
                
                if not page_data:
                    break
                
                # 添加所有数据到结果列表
                all_stockouts.extend(page_data)
                
                self.logger.info(f"第{page_no + 1}页查询到{len(page_data)}条出库明细")
                
                page_no += 1
                
                # 检查是否还有更多数据
                if len(page_data) < page_size:
                    self.logger.info("已到达最后一页数据")
                    break

                # 防止无限循环，但增加限制
                if page_no >= max_pages:
                    self.logger.warning(f"已查询{max_pages}页，停止查询。如需更多数据，请调整时间范围")
                    break

                # 每查询10页显示一次进度
                if page_no % 10 == 0 and page_no > 0:
                    self.logger.info(f"已查询{page_no}页，累计{len(all_stockouts)}条记录...")
                
            except Exception as e:
                self.logger.error(f"查询失败: {str(e)}")
                break
        
        self.logger.info(f"共查询到{len(all_stockouts)}条销售出库明细")
        return all_stockouts
    
    def filter_canceled_stockouts(self, stockouts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从出库明细中筛选已取消的记录
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            已取消的出库明细列表
        """
        canceled_stockouts = []
        
        for stockout in stockouts:
            if isinstance(stockout, dict):
                status = str(stockout.get('status', ''))
                if status == '5':  # 已取消状态
                    canceled_stockouts.append(stockout)
        
        self.logger.info(f"从{len(stockouts)}条出库明细中筛选出{len(canceled_stockouts)}条已取消记录")
        return canceled_stockouts
    
    def extract_jd_logistics_numbers(self, stockouts: List[Dict[str, Any]]) -> List[str]:
        """
        从出库明细中提取京东物流单号
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            京东物流单号列表
        """
        jd_numbers = []
        
        for stockout in stockouts:
            # 尝试从不同字段获取物流单号
            logistics_fields = ['logistics_no', 'express_no', 'tracking_no', 'waybill_no', 'express_number']
            
            for field in logistics_fields:
                logistics_no = stockout.get(field, '')
                if logistics_no and str(logistics_no).upper().startswith('JD'):
                    jd_numbers.append(str(logistics_no))
                    self.logger.debug(f"找到京东物流单号: {logistics_no} (出库单号: {stockout.get('stockout_no', '未知')})")
                    break
        
        # 去重并排序
        unique_jd_numbers = sorted(list(set(jd_numbers)))
        self.logger.info(f"共提取到{len(unique_jd_numbers)}个京东物流单号")
        
        return unique_jd_numbers
    
    def get_stockout_statistics(self, stockouts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取出库明细统计信息
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_count': len(stockouts),
            'status_count': {},
            'logistics_count': {},
            'jd_logistics_count': 0,
            'canceled_count': 0,
            'shipped_count': 0
        }
        
        for stockout in stockouts:
            # 统计状态
            status = str(stockout.get('status', ''))
            stats['status_count'][status] = stats['status_count'].get(status, 0) + 1
            
            if status == '5':
                stats['canceled_count'] += 1
            elif status == '95':
                stats['shipped_count'] += 1
            
            # 统计物流公司
            logistics_code = stockout.get('logistics_code', '')
            if logistics_code:
                stats['logistics_count'][logistics_code] = stats['logistics_count'].get(logistics_code, 0) + 1
            
            # 统计京东物流单号
            logistics_no = stockout.get('logistics_no', '')
            if logistics_no and str(logistics_no).upper().startswith('JD'):
                stats['jd_logistics_count'] += 1
        
        return stats
    
    def get_canceled_jd_logistics_today(self) -> Dict[str, Any]:
        """
        获取当天已取消的京东物流单号
        
        Returns:
            处理结果字典
        """
        try:
            # 1. 获取当天所有销售出库明细
            all_stockouts = self.get_all_stockouts_today()
            
            # 2. 筛选已取消的出库明细
            canceled_stockouts = self.filter_canceled_stockouts(all_stockouts)
            
            # 3. 从已取消的出库明细中提取京东物流单号
            jd_numbers = self.extract_jd_logistics_numbers(canceled_stockouts)
            
            # 4. 获取统计信息
            all_stats = self.get_stockout_statistics(all_stockouts)
            canceled_stats = self.get_stockout_statistics(canceled_stockouts)
            
            result = {
                'success': True,
                'total_stockouts': len(all_stockouts),
                'canceled_stockouts': len(canceled_stockouts),
                'jd_logistics_numbers': jd_numbers,
                'jd_logistics_count': len(jd_numbers),
                'all_statistics': all_stats,
                'canceled_statistics': canceled_stats,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self.logger.info(f"处理完成: 总出库单{len(all_stockouts)}个，已取消{len(canceled_stockouts)}个，京东物流单号{len(jd_numbers)}个")
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取已取消京东物流单号失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_stockouts': 0,
                'canceled_stockouts': 0,
                'jd_logistics_numbers': [],
                'jd_logistics_count': 0,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def print_detailed_report(self, result: Dict[str, Any]):
        """
        打印详细报告
        
        Args:
            result: 处理结果字典
        """
        if not result['success']:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
            return
        
        print("=" * 50)
        print("📊 销售出库明细统计报告")
        print("=" * 50)
        print(f"📅 处理时间: {result['process_time']}")
        print(f"📦 总出库单数: {result['total_stockouts']}")
        print(f"❌ 已取消出库单数: {result['canceled_stockouts']}")
        print(f"🚚 京东物流单号数: {result['jd_logistics_count']}")
        
        if result['jd_logistics_numbers']:
            print("\n📋 京东物流单号列表:")
            for i, number in enumerate(result['jd_logistics_numbers'], 1):
                print(f"  {i}. {number}")
        
        # 打印状态统计
        all_stats = result['all_statistics']
        print(f"\n📈 状态统计 (总计):")
        for status, count in all_stats['status_count'].items():
            status_name = {
                '5': '已取消',
                '95': '已发货',
                '30': '待审核'
            }.get(status, f'状态{status}')
            print(f"  {status_name}: {count}个")
        
        # 打印物流公司统计
        if all_stats['logistics_count']:
            print(f"\n🚛 物流公司统计:")
            for logistics_code, count in all_stats['logistics_count'].items():
                print(f"  {logistics_code}: {count}个")

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主函数 - 用于测试"""
    logger = setup_logging()
    
    try:
        # 创建管理器
        manager = StockoutDetailsManager()
        
        # 获取当天已取消的京东物流单号
        logger.info("开始获取当天已取消的京东物流单号...")
        result = manager.get_canceled_jd_logistics_today()
        
        # 打印详细报告
        manager.print_detailed_report(result)
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == '__main__':
    main()
