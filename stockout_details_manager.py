#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
销售出库明细管理模块
专门用于获取已取消的销售出库明细并提取京东物流单号
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from wdt_post_client import WDTPostClient

class StockoutDetailsManager:
    """销售出库明细管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.client = WDTPostClient()
        self.logger = logging.getLogger(__name__)
        
    def get_all_stockouts_today(self) -> List[Dict[str, Any]]:
        """
        获取当天所有的销售出库明细
        
        Returns:
            所有出库明细列表
        """
        # 获取当天时间范围
        now = datetime.now()
        start_time = datetime.combine(now.date(), datetime.min.time())
        end_time = now
        
        return self.get_all_stockouts(
            start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time.strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def get_all_stockouts(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内所有的销售出库明细
        
        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)
            
        Returns:
            所有出库明细列表
        """
        self.logger.info(f"开始查询{start_time}至{end_time}期间所有销售出库明细...")
        
        page_size = 100
        page_no = 0
        all_stockouts = []
        max_pages = 1000  # 增加最大页数限制，支持更多数据

        # 构建查询参数
        base_params = {
            "start_consign_time": start_time,
            "end_consign_time": end_time,
            "page_size": page_size
        }
        
        while True:
            try:
                self.logger.debug(f"正在查询第{page_no + 1}页...")
                
                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }
                
                # 调用出库单查询API
                result = self.client.call_api('stockout.query', current_params)
                
                if not result:
                    break
                
                # 获取数据
                content = result.get('content', [])
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', []) or content.get('stockouts', [])
                
                if not page_data:
                    break
                
                # 添加所有数据到结果列表
                all_stockouts.extend(page_data)
                
                self.logger.info(f"第{page_no + 1}页查询到{len(page_data)}条出库明细")
                
                page_no += 1
                
                # 检查是否还有更多数据
                if len(page_data) < page_size:
                    self.logger.info("已到达最后一页数据")
                    break

                # 防止无限循环，但增加限制
                if page_no >= max_pages:
                    self.logger.warning(f"已查询{max_pages}页，停止查询。如需更多数据，请调整时间范围")
                    break

                # 每查询10页显示一次进度
                if page_no % 10 == 0 and page_no > 0:
                    self.logger.info(f"已查询{page_no}页，累计{len(all_stockouts)}条记录...")
                
            except Exception as e:
                self.logger.error(f"查询失败: {str(e)}")
                break
        
        self.logger.info(f"共查询到{len(all_stockouts)}条销售出库明细")
        return all_stockouts
    
    def filter_canceled_stockouts(self, stockouts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从出库明细中筛选已取消的记录
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            已取消的出库明细列表
        """
        canceled_stockouts = []
        
        for stockout in stockouts:
            if isinstance(stockout, dict):
                status = str(stockout.get('status', ''))
                if status == '5':  # 已取消状态
                    canceled_stockouts.append(stockout)
        
        self.logger.info(f"从{len(stockouts)}条出库明细中筛选出{len(canceled_stockouts)}条已取消记录")
        return canceled_stockouts
    
    def extract_jd_logistics_numbers(self, stockouts: List[Dict[str, Any]]) -> List[str]:
        """
        从出库明细中提取京东物流单号
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            京东物流单号列表
        """
        jd_numbers = []
        
        for stockout in stockouts:
            # 尝试从不同字段获取物流单号
            logistics_fields = ['logistics_no', 'express_no', 'tracking_no', 'waybill_no', 'express_number']
            
            for field in logistics_fields:
                logistics_no = stockout.get(field, '')
                if logistics_no and str(logistics_no).upper().startswith('JD'):
                    jd_numbers.append(str(logistics_no))
                    self.logger.debug(f"找到京东物流单号: {logistics_no} (出库单号: {stockout.get('stockout_no', '未知')})")
                    break
        
        # 去重并排序
        unique_jd_numbers = sorted(list(set(jd_numbers)))
        self.logger.info(f"共提取到{len(unique_jd_numbers)}个京东物流单号")
        
        return unique_jd_numbers

    def extract_jd_from_sales_orders(self, orders: List[Dict[str, Any]]) -> List[str]:
        """
        从销售订单中提取京东物流信息

        Args:
            orders: 销售订单列表

        Returns:
            京东物流单号列表
        """
        jd_numbers = []
        jd_orders = []  # 存储京东订单信息用于调试

        for order in orders:
            # 检查物流编码是否为京东
            logistics_code = order.get('logistics_code', '')
            trade_no = order.get('trade_no', '未知')

            if logistics_code and str(logistics_code).upper() in ['JBD', 'JD', 'JINGDONG']:
                jd_orders.append({
                    'trade_no': trade_no,
                    'logistics_code': logistics_code,
                    'platform': order.get('platform_name', ''),
                    'created': order.get('created', ''),
                    'modified': order.get('modified', '')
                })

                # 如果是京东物流，尝试获取物流单号
                logistics_fields = ['logistics_no', 'express_no', 'tracking_no', 'waybill_no']

                found_logistics_no = False
                for field in logistics_fields:
                    logistics_no = order.get(field, '')
                    if logistics_no and str(logistics_no).strip():
                        # 如果物流单号以JD开头，或者物流编码是京东的，都认为是京东物流单号
                        if str(logistics_no).upper().startswith('JD') or logistics_code.upper() in ['JBD', 'JD']:
                            jd_numbers.append(str(logistics_no))
                            self.logger.info(f"✅ 找到京东物流单号: {logistics_no} (订单号: {trade_no}, 平台: {order.get('platform_name', '')})")
                            found_logistics_no = True
                            break

                if not found_logistics_no:
                    # 对于没有物流单号的京东订单，使用订单号作为标识
                    virtual_logistics_no = f"JD-{trade_no}"
                    jd_numbers.append(virtual_logistics_no)
                    self.logger.info(f"🔄 京东订单无物流单号，使用虚拟单号: {virtual_logistics_no} (订单号: {trade_no}, 平台: {order.get('platform_name', '')})")

        # 显示京东订单统计
        if jd_orders:
            self.logger.info(f"📊 京东订单统计: 共{len(jd_orders)}个京东订单")
            for jd_order in jd_orders:
                self.logger.info(f"   订单号: {jd_order['trade_no']}, 平台: {jd_order['platform']}, 物流: {jd_order['logistics_code']}")

        # 去重并排序
        unique_jd_numbers = sorted(list(set(jd_numbers)))
        self.logger.info(f"🎯 从销售订单中提取到{len(unique_jd_numbers)}个京东物流单号")

        return unique_jd_numbers

    def get_sales_order_statistics(self, orders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取销售订单统计信息

        Args:
            orders: 销售订单列表

        Returns:
            统计信息字典
        """
        stats = {
            'total_count': len(orders),
            'status_count': {},
            'logistics_count': {},
            'platform_count': {},
            'jd_logistics_count': 0
        }

        for order in orders:
            # 统计状态
            status = str(order.get('trade_status', ''))
            stats['status_count'][status] = stats['status_count'].get(status, 0) + 1

            # 统计物流公司
            logistics_code = order.get('logistics_code', '')
            if logistics_code:
                stats['logistics_count'][logistics_code] = stats['logistics_count'].get(logistics_code, 0) + 1

                # 统计京东物流
                if logistics_code.upper() in ['JBD', 'JD', 'JINGDONG']:
                    stats['jd_logistics_count'] += 1

            # 统计平台
            platform = order.get('platform_name', '')
            if platform:
                stats['platform_count'][platform] = stats['platform_count'].get(platform, 0) + 1

        return stats
    
    def get_stockout_statistics(self, stockouts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取出库明细统计信息
        
        Args:
            stockouts: 出库明细列表
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_count': len(stockouts),
            'status_count': {},
            'logistics_count': {},
            'jd_logistics_count': 0,
            'canceled_count': 0,
            'shipped_count': 0
        }
        
        for stockout in stockouts:
            # 统计状态
            status = str(stockout.get('status', ''))
            stats['status_count'][status] = stats['status_count'].get(status, 0) + 1
            
            if status == '5':
                stats['canceled_count'] += 1
            elif status == '95':
                stats['shipped_count'] += 1
            
            # 统计物流公司
            logistics_code = stockout.get('logistics_code', '')
            if logistics_code:
                stats['logistics_count'][logistics_code] = stats['logistics_count'].get(logistics_code, 0) + 1
            
            # 统计京东物流单号
            logistics_no = stockout.get('logistics_no', '')
            if logistics_no and str(logistics_no).upper().startswith('JD'):
                stats['jd_logistics_count'] += 1
        
        return stats
    
    def get_canceled_sales_orders_today(self) -> List[Dict[str, Any]]:
        """
        获取当天已取消的销售订单

        Returns:
            已取消销售订单列表
        """
        # 获取当天时间范围
        now = datetime.now()
        start_time = datetime.combine(now.date(), datetime.min.time())
        end_time = now

        return self.get_canceled_sales_orders(
            start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time.strftime('%Y-%m-%d %H:%M:%S')
        )

    def get_canceled_sales_orders(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内已取消的销售订单

        Args:
            start_time: 开始时间 (YYYY-MM-DD HH:MM:SS)
            end_time: 结束时间 (YYYY-MM-DD HH:MM:SS)

        Returns:
            已取消销售订单列表
        """
        self.logger.info(f"开始查询{start_time}至{end_time}期间已取消的销售订单...")

        page_size = 100
        page_no = 0
        all_orders = []
        max_pages = 1000

        # 构建查询参数
        base_params = {
            "start_time": start_time,
            "end_time": end_time,
            "trade_status": 5,  # 已取消状态
            "page_size": page_size
        }

        while True:
            try:
                self.logger.debug(f"正在查询第{page_no + 1}页...")

                # 当前页参数
                current_params = {
                    **base_params,
                    "page_no": page_no
                }

                # 调用销售订单查询API
                result = self.client.query_sales_trades(**current_params)

                if not result:
                    break

                # 获取数据
                content = result.get('content', [])
                page_data = []
                if isinstance(content, list):
                    page_data = content
                elif isinstance(content, dict):
                    page_data = content.get('content', []) or content.get('trades', [])

                if not page_data:
                    break

                # 添加所有数据到结果列表
                all_orders.extend(page_data)

                self.logger.info(f"第{page_no + 1}页查询到{len(page_data)}条已取消销售订单")

                page_no += 1

                # 检查是否还有更多数据
                if len(page_data) < page_size:
                    self.logger.info("已到达最后一页数据")
                    break

                # 防止无限循环
                if page_no >= max_pages:
                    self.logger.warning(f"已查询{max_pages}页，停止查询。如需更多数据，请调整时间范围")
                    break

                # 每查询10页显示一次进度
                if page_no % 10 == 0 and page_no > 0:
                    self.logger.info(f"已查询{page_no}页，累计{len(all_orders)}条记录...")

            except Exception as e:
                self.logger.error(f"查询失败: {str(e)}")
                break

        self.logger.info(f"共查询到{len(all_orders)}条已取消的销售订单")
        return all_orders

    def get_stockouts_by_trade_nos(self, trade_nos: List[str]) -> List[Dict[str, Any]]:
        """
        根据销售订单号查询对应的出库单

        Args:
            trade_nos: 销售订单号列表

        Returns:
            出库单列表
        """
        self.logger.info(f"开始根据{len(trade_nos)}个销售订单号查询出库单...")

        all_stockouts = []

        # 使用有效的货主编号（移除无效的changhe）
        owner_nos = ["AJT-XLSWDT", "AJT-QCHTMWDT", "AJT-BQPOPWDT"]

        for trade_no in trade_nos:
            found_stockout = False

            for owner_no in owner_nos:
                try:
                    # 使用trade_no和owner_no查询出库单
                    params = {
                        "owner_no": owner_no,
                        "trade_no": trade_no,
                        "page_size": 100,
                        "page_no": 0
                    }

                    result = self.client.call_api('stockout.query', params)

                    if result and result.get('flag') == 'success':
                        content = result.get('content', [])
                        if isinstance(content, list) and content:
                            all_stockouts.extend(content)
                            self.logger.info(f"✅ 订单号 {trade_no} (货主: {owner_no}) 查询到 {len(content)} 个出库单")
                            found_stockout = True
                            break

                except Exception as e:
                    self.logger.debug(f"查询订单号 {trade_no} (货主: {owner_no}) 失败: {e}")
                    continue

            if not found_stockout:
                self.logger.warning(f"⚠️ 订单号 {trade_no} 未找到对应的出库单")

        self.logger.info(f"共查询到{len(all_stockouts)}个出库单")
        return all_stockouts

    def get_logistics_from_src_tids(self, src_tids_list: List[str]) -> List[str]:
        """
        通过原始订单号查询物流单号

        Args:
            src_tids_list: 原始订单号列表

        Returns:
            物流单号列表
        """
        self.logger.info(f"开始通过{len(src_tids_list)}个原始订单号查询物流单号...")

        logistics_numbers = []

        for src_tids in src_tids_list:
            try:
                # 使用src_tid查询出库单
                params = {
                    "src_tid": src_tids,
                    "page_size": 100,
                    "page_no": 0
                }

                result = self.client.call_api('stockout.query', params)

                if result and result.get('flag') == 'success':
                    content = result.get('content', [])
                    if isinstance(content, list):
                        for stockout in content:
                            logistics_no = stockout.get('logistics_no', '')
                            logistics_code = stockout.get('logistics_code', '')

                            if logistics_no and logistics_code:
                                # 检查是否为京东物流
                                if logistics_code.upper() in ['JBD', 'JD', 'JINGDONG']:
                                    logistics_numbers.append(logistics_no)
                                    self.logger.info(f"✅ 通过原始订单号 {src_tids} 找到京东物流单号: {logistics_no}")

            except Exception as e:
                self.logger.debug(f"通过原始订单号 {src_tids} 查询失败: {e}")
                continue

        unique_logistics = sorted(list(set(logistics_numbers)))
        self.logger.info(f"通过原始订单号共找到{len(unique_logistics)}个京东物流单号")
        return unique_logistics

    def get_canceled_jd_logistics_today(self) -> Dict[str, Any]:
        """
        获取当天已取消的京东物流单号

        Returns:
            处理结果字典
        """
        try:
            # 1. 获取当天已取消的销售订单
            canceled_orders = self.get_canceled_sales_orders_today()

            # 2. 多种策略获取京东物流单号
            all_jd_numbers = []

            # 策略1: 从销售订单中直接提取物流信息
            sales_jd_numbers = self.extract_jd_from_sales_orders(canceled_orders)
            all_jd_numbers.extend(sales_jd_numbers)

            # 策略2: 通过原始订单号查询物流单号
            src_tids_list = [order.get('src_tids', '') for order in canceled_orders if order.get('src_tids')]
            if src_tids_list:
                self.logger.info(f"尝试通过{len(src_tids_list)}个原始订单号查询物流单号...")
                src_jd_numbers = self.get_logistics_from_src_tids(src_tids_list)
                all_jd_numbers.extend(src_jd_numbers)

            # 策略3: 根据订单号查询出库单获取物流单号
            trade_nos = [order.get('trade_no', '') for order in canceled_orders if order.get('trade_no')]
            if trade_nos:
                self.logger.info(f"尝试通过{len(trade_nos)}个销售订单号查询出库单...")
                stockouts = self.get_stockouts_by_trade_nos(trade_nos)
                if stockouts:
                    stockout_jd_numbers = self.extract_jd_logistics_numbers(stockouts)
                    all_jd_numbers.extend(stockout_jd_numbers)

            # 去重并排序
            jd_numbers = sorted(list(set(all_jd_numbers)))

            # 3. 获取统计信息
            canceled_stats = self.get_sales_order_statistics(canceled_orders)

            result = {
                'success': True,
                'total_canceled_orders': len(canceled_orders),
                'jd_logistics_numbers': jd_numbers,
                'jd_logistics_count': len(jd_numbers),
                'canceled_statistics': canceled_stats,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            self.logger.info(f"🎉 处理完成: 已取消订单{len(canceled_orders)}个，京东物流单号{len(jd_numbers)}个")

            return result

        except Exception as e:
            self.logger.error(f"获取已取消京东物流单号失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_canceled_orders': 0,
                'jd_logistics_numbers': [],
                'jd_logistics_count': 0,
                'process_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def print_detailed_report(self, result: Dict[str, Any]):
        """
        打印详细报告
        
        Args:
            result: 处理结果字典
        """
        if not result['success']:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
            return
        
        print("=" * 50)
        print("📊 京东取消订单统计报告")
        print("=" * 50)
        print(f"📅 处理时间: {result['process_time']}")
        print(f"📦 已取消销售订单数: {result.get('total_canceled_orders', 0)}")
        print(f"🚚 京东物流单号数: {result['jd_logistics_count']}")

        if result['jd_logistics_numbers']:
            print("\n📋 京东物流单号列表:")
            for i, number in enumerate(result['jd_logistics_numbers'], 1):
                print(f"  {i}. {number}")
        else:
            print("\n✅ 今天没有已取消的京东订单")

        # 打印统计信息
        if 'canceled_statistics' in result:
            stats = result['canceled_statistics']
            print(f"\n📈 已取消订单统计:")

            # 状态统计
            if 'status_count' in stats:
                for status, count in stats['status_count'].items():
                    status_name = {
                        '5': '已取消',
                        '95': '已发货',
                        '30': '待审核'
                    }.get(status, f'状态{status}')
                    print(f"  {status_name}: {count}个")

            # 物流公司统计
            if 'logistics_count' in stats and stats['logistics_count']:
                print(f"\n🚛 物流公司统计:")
                for logistics_code, count in stats['logistics_count'].items():
                    logistics_name = {
                        'JBD': '京东快递',
                        'SF': '顺丰速运',
                        'YTO': '圆通速递',
                        'ZTO': '中通快递'
                    }.get(logistics_code, logistics_code)
                    print(f"  {logistics_name}({logistics_code}): {count}个")

            # 平台统计
            if 'platform_count' in stats and stats['platform_count']:
                print(f"\n🛒 平台统计:")
                for platform, count in stats['platform_count'].items():
                    print(f"  {platform}: {count}个")

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主函数 - 用于测试"""
    logger = setup_logging()
    
    try:
        # 创建管理器
        manager = StockoutDetailsManager()
        
        # 获取当天已取消的京东物流单号
        logger.info("开始获取当天已取消的京东物流单号...")
        result = manager.get_canceled_jd_logistics_today()
        
        # 打印详细报告
        manager.print_detailed_report(result)
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == '__main__':
    main()
