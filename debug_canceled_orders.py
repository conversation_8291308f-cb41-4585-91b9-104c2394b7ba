#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试已取消订单查询问题
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
import json

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_stockout_query():
    """调试出库单查询"""
    logger = setup_logging()
    client = WDTPostClient()
    
    # 获取当天时间范围
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"=== 调试出库单查询 ===")
    logger.info(f"查询时间范围: {start_str} 至 {end_str}")
    
    # 测试不同的查询参数
    test_cases = [
        {
            "name": "基础查询",
            "params": {
                "start_consign_time": start_str,
                "end_consign_time": end_str,
                "page_size": 10,
                "page_no": 0
            }
        },
        {
            "name": "查询已取消状态",
            "params": {
                "start_consign_time": start_str,
                "end_consign_time": end_str,
                "status": 5,  # 已取消状态
                "page_size": 10,
                "page_no": 0
            }
        },
        {
            "name": "扩大时间范围（最近3天）",
            "params": {
                "start_consign_time": (now - timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S'),
                "end_consign_time": end_str,
                "page_size": 10,
                "page_no": 0
            }
        },
        {
            "name": "使用修改时间查询",
            "params": {
                "start_modified": start_str,
                "end_modified": end_str,
                "page_size": 10,
                "page_no": 0
            }
        },
        {
            "name": "使用创建时间查询",
            "params": {
                "start_time": start_str,
                "end_time": end_str,
                "page_size": 10,
                "page_no": 0
            }
        }
    ]
    
    for test_case in test_cases:
        logger.info(f"\n--- {test_case['name']} ---")
        logger.info(f"查询参数: {json.dumps(test_case['params'], ensure_ascii=False, indent=2)}")
        
        try:
            result = client.call_api('stockout.query', test_case['params'])
            
            if result:
                logger.info(f"API响应成功")
                logger.info(f"响应键: {list(result.keys())}")
                
                # 分析响应内容
                if 'content' in result:
                    content = result['content']
                    logger.info(f"content类型: {type(content)}")
                    
                    if isinstance(content, list):
                        logger.info(f"查询到 {len(content)} 条记录")
                        
                        # 分析状态分布
                        status_count = {}
                        for item in content:
                            if isinstance(item, dict):
                                status = str(item.get('status', ''))
                                status_count[status] = status_count.get(status, 0) + 1
                        
                        logger.info(f"状态分布: {status_count}")
                        
                        # 显示前3条记录的详细信息
                        for i, item in enumerate(content[:3]):
                            if isinstance(item, dict):
                                logger.info(f"记录 {i+1}:")
                                logger.info(f"  出库单号: {item.get('stockout_no', '未知')}")
                                logger.info(f"  状态: {item.get('status', '未知')}")
                                logger.info(f"  物流单号: {item.get('logistics_no', '未知')}")
                                logger.info(f"  创建时间: {item.get('created', '未知')}")
                                logger.info(f"  修改时间: {item.get('modified', '未知')}")
                                logger.info(f"  发货时间: {item.get('consign_time', '未知')}")
                    else:
                        logger.info(f"content内容: {content}")
                
                if 'total' in result:
                    logger.info(f"总记录数: {result['total']}")
                
                # 显示完整响应（仅前500字符）
                response_str = json.dumps(result, ensure_ascii=False)
                if len(response_str) > 500:
                    logger.debug(f"完整响应（截取）: {response_str[:500]}...")
                else:
                    logger.debug(f"完整响应: {response_str}")
            else:
                logger.error("API响应为空")
                
        except Exception as e:
            logger.error(f"查询失败: {e}")
    
    # 测试销售订单查询作为对比
    logger.info(f"\n=== 对比：销售订单查询 ===")
    try:
        sales_params = {
            "start_time": start_str,
            "end_time": end_str,
            "trade_status": 5,  # 已取消
            "page_size": 10,
            "page_no": 0
        }
        
        logger.info(f"销售订单查询参数: {json.dumps(sales_params, ensure_ascii=False, indent=2)}")
        
        # 使用现有的销售订单查询方法
        sales_result = client.query_sales_trades(**sales_params)
        
        if sales_result:
            logger.info(f"销售订单API响应成功")
            logger.info(f"销售订单响应键: {list(sales_result.keys())}")
            
            if 'content' in sales_result:
                content = sales_result['content']
                logger.info(f"销售订单查询到 {len(content)} 条记录")
                
                for i, trade in enumerate(content[:3]):
                    if isinstance(trade, dict):
                        logger.info(f"销售订单 {i+1}:")
                        logger.info(f"  订单号: {trade.get('trade_no', '未知')}")
                        logger.info(f"  状态: {trade.get('trade_status', '未知')}")
                        logger.info(f"  创建时间: {trade.get('created', '未知')}")
                        logger.info(f"  修改时间: {trade.get('modified', '未知')}")
        
    except Exception as e:
        logger.error(f"销售订单查询失败: {e}")

def debug_api_permissions():
    """调试API权限"""
    logger = setup_logging()
    client = WDTPostClient()
    
    logger.info(f"\n=== 调试API权限 ===")
    
    # 测试基本连接
    try:
        if client.test_connection():
            logger.info("✅ API连接正常")
        else:
            logger.error("❌ API连接失败")
            return
    except Exception as e:
        logger.error(f"❌ API连接测试异常: {e}")
        return
    
    # 测试不同API接口的权限
    test_apis = [
        {
            "name": "出库单查询",
            "method": "stockout.query",
            "params": {"page_size": 1, "page_no": 0}
        },
        {
            "name": "销售订单查询", 
            "method": "sales.trade.query",
            "params": {
                "start_time": datetime.now().strftime('%Y-%m-%d 00:00:00'),
                "end_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "page_size": 1,
                "page_no": 0
            }
        }
    ]
    
    for api_test in test_apis:
        logger.info(f"\n--- 测试 {api_test['name']} ---")
        try:
            result = client.call_api(api_test['method'], api_test['params'])
            
            if result and result.get('flag') == 'success':
                logger.info(f"✅ {api_test['name']} 权限正常")
                if 'content' in result:
                    content = result['content']
                    if isinstance(content, list):
                        logger.info(f"   返回 {len(content)} 条记录")
                    else:
                        logger.info(f"   返回内容类型: {type(content)}")
            else:
                logger.error(f"❌ {api_test['name']} 权限异常")
                if result:
                    logger.error(f"   错误信息: {result.get('message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"❌ {api_test['name']} 调用异常: {e}")

if __name__ == '__main__':
    print("🔍 开始调试已取消订单查询问题...")
    
    # 调试API权限
    debug_api_permissions()
    
    # 调试出库单查询
    debug_stockout_query()
    
    print("\n🎯 调试完成，请查看日志分析问题原因")
