# 旺店通数据自动获取工具依赖包
# 用于pip install -r requirements.txt安装所有依赖

# 核心依赖
requests>=2.28.0          # HTTP请求库，用于API调用和微信机器人
openpyxl>=3.0.0          # Excel文件读写库
python-dotenv>=0.19.0    # 环境变量加载库

# 本地微信发送依赖
pyautogui>=0.9.54        # 自动化GUI操作库
pyperclip>=1.8.2         # 剪贴板操作库
pywin32>=306             # Windows API库

# 打包依赖
pyinstaller>=5.0.0       # 程序打包工具

# 可选依赖（用于开发和测试）
# pytest>=7.0.0          # 测试框架
# black>=22.0.0           # 代码格式化工具
# flake8>=4.0.0           # 代码检查工具

# 标准库（Python内置，无需安装）
# tkinter                 # GUI界面库（Python内置）
# threading               # 多线程库（Python内置）
# time                    # 时间处理库（Python内置）
# os                      # 操作系统接口库（Python内置）
# json                    # JSON处理库（Python内置）
# hashlib                 # 哈希算法库（Python内置）
# logging                 # 日志库（Python内置）
# datetime                # 日期时间库（Python内置）
# typing                  # 类型提示库（Python内置）
