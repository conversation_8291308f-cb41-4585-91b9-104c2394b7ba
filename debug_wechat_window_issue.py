#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试微信窗口和货主编号问题
"""

import logging
import time
import pyautogui
import win32gui
import win32con
from local_wechat_sender import WeChatWindowFinder, LocalWeChatSender
from wdt_post_client import WDTPostClient

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_wechat_windows():
    """调试微信窗口问题"""
    logger = setup_logging()
    
    logger.info("=== 调试微信窗口问题 ===")
    
    # 1. 检查所有微信相关窗口
    logger.info("1. 检查所有微信相关窗口...")
    
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            
            # 检查是否包含微信相关关键词
            if any(keyword in window_text.lower() for keyword in ['微信', 'wechat']) or \
               any(keyword in class_name.lower() for keyword in ['wechat', 'chrome_widgetwin']):
                rect = win32gui.GetWindowRect(hwnd)
                windows.append({
                    'hwnd': hwnd,
                    'title': window_text,
                    'class_name': class_name,
                    'rect': rect,
                    'visible': win32gui.IsWindowVisible(hwnd),
                    'enabled': win32gui.IsWindowEnabled(hwnd)
                })
        return True
    
    all_windows = []
    win32gui.EnumWindows(enum_windows_callback, all_windows)
    
    logger.info(f"找到 {len(all_windows)} 个微信相关窗口:")
    for i, window in enumerate(all_windows):
        logger.info(f"  窗口 {i+1}:")
        logger.info(f"    句柄: {window['hwnd']}")
        logger.info(f"    标题: '{window['title']}'")
        logger.info(f"    类名: {window['class_name']}")
        logger.info(f"    位置: {window['rect']}")
        logger.info(f"    可见: {window['visible']}")
        logger.info(f"    启用: {window['enabled']}")
    
    # 2. 使用现有的微信窗口查找器
    logger.info("\n2. 使用现有的微信窗口查找器...")
    wechat_windows = WeChatWindowFinder.get_all_wechat_windows()
    
    if wechat_windows:
        logger.info(f"WeChatWindowFinder 找到 {len(wechat_windows)} 个窗口:")
        for i, window in enumerate(wechat_windows):
            logger.info(f"  窗口 {i+1}: {window}")
    else:
        logger.error("WeChatWindowFinder 未找到任何微信窗口")
    
    # 3. 测试微信发送器
    logger.info("\n3. 测试微信发送器...")
    
    contact_name = input("请输入要测试的联系人名称 (建议使用'文件传输助手'): ").strip()
    if not contact_name:
        contact_name = "文件传输助手"
    
    try:
        sender = LocalWeChatSender(contact_name)
        
        # 测试查找微信窗口
        logger.info("测试查找微信窗口...")
        hwnd = sender.find_wechat_window()
        
        if hwnd:
            logger.info(f"✅ 成功找到微信窗口: {hwnd}")
            
            # 获取窗口详细信息
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            
            logger.info(f"  窗口标题: '{window_text}'")
            logger.info(f"  窗口类名: {class_name}")
            logger.info(f"  窗口位置: {rect}")
            
            # 测试激活窗口
            logger.info("测试激活微信窗口...")
            if sender.activate_wechat_window(hwnd):
                logger.info("✅ 微信窗口激活成功")
                
                # 等待用户确认微信窗口是否正确激活
                input("请检查微信窗口是否已激活，然后按回车继续...")
                
                # 测试搜索联系人
                logger.info(f"测试搜索联系人 '{contact_name}'...")
                if sender.search_contact(contact_name):
                    logger.info("✅ 联系人搜索成功")
                    
                    # 测试发送消息
                    test_message = f"微信窗口调试测试 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
                    logger.info(f"测试发送消息: {test_message}")
                    
                    if sender.send_message_to_clipboard(test_message):
                        logger.info("✅ 消息发送成功")
                    else:
                        logger.error("❌ 消息发送失败")
                else:
                    logger.error("❌ 联系人搜索失败")
            else:
                logger.error("❌ 微信窗口激活失败")
        else:
            logger.error("❌ 未找到微信窗口")
            
            # 提供解决建议
            logger.info("\n💡 解决建议:")
            logger.info("1. 确保微信已启动并登录")
            logger.info("2. 确保微信窗口没有被最小化")
            logger.info("3. 尝试重启微信")
            logger.info("4. 检查微信版本是否兼容")
            
    except Exception as e:
        logger.error(f"微信发送器测试异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")

def debug_owner_no_issue():
    """调试货主编号问题"""
    logger = setup_logging()
    
    logger.info("\n=== 调试货主编号问题 ===")
    
    client = WDTPostClient()
    
    # 测试不同的货主编号
    test_owner_nos = [
        "changhe",
        "AJT-XLSWDT", 
        "AJT-QCHTMWDT",
        "AJT-BQPOPWDT",
        "",  # 空值测试
    ]
    
    # 使用一个已知的订单号进行测试
    test_trade_no = "LL202507161950"
    
    logger.info(f"使用订单号 {test_trade_no} 测试不同货主编号...")
    
    for owner_no in test_owner_nos:
        logger.info(f"\n--- 测试货主编号: '{owner_no}' ---")
        
        try:
            if owner_no:
                params = {
                    "owner_no": owner_no,
                    "trade_no": test_trade_no,
                    "page_size": 10,
                    "page_no": 0
                }
            else:
                # 测试不使用owner_no参数
                params = {
                    "trade_no": test_trade_no,
                    "page_size": 10,
                    "page_no": 0
                }
            
            logger.info(f"查询参数: {params}")
            
            result = client.call_api('stockout.query', params)
            
            if result:
                if result.get('flag') == 'success':
                    content = result.get('content', [])
                    logger.info(f"✅ 查询成功，返回 {len(content)} 条记录")
                    
                    if content:
                        # 显示第一条记录的详细信息
                        first_record = content[0]
                        logger.info(f"  出库单号: {first_record.get('stockout_no', '未知')}")
                        logger.info(f"  物流单号: {first_record.get('logistics_no', '未知')}")
                        logger.info(f"  物流编码: {first_record.get('logistics_code', '未知')}")
                        logger.info(f"  货主编号: {first_record.get('owner_no', '未知')}")
                else:
                    logger.error(f"❌ 查询失败: {result.get('message', '未知错误')}")
            else:
                logger.error("❌ API调用返回空结果")
                
        except Exception as e:
            logger.error(f"❌ 查询异常: {e}")
    
    # 查询可用的货主列表
    logger.info("\n--- 尝试查询可用的货主列表 ---")
    try:
        # 尝试查询货主信息
        params = {
            "page_size": 100,
            "page_no": 0
        }
        
        # 注意：这个API可能不存在，仅作为尝试
        result = client.call_api('owner.query', params)
        
        if result and result.get('flag') == 'success':
            owners = result.get('content', [])
            logger.info(f"找到 {len(owners)} 个货主:")
            for owner in owners[:10]:  # 只显示前10个
                logger.info(f"  货主编号: {owner.get('owner_no', '未知')}")
                logger.info(f"  货主名称: {owner.get('owner_name', '未知')}")
        else:
            logger.info("无法查询货主列表（API可能不支持）")
            
    except Exception as e:
        logger.info(f"查询货主列表失败: {e}")

def provide_solutions():
    """提供解决方案"""
    logger = setup_logging()
    
    logger.info("\n=== 解决方案建议 ===")
    
    logger.info("🔧 微信窗口问题解决方案:")
    logger.info("1. 确保微信已正确启动并登录")
    logger.info("2. 确保微信窗口处于可见状态（不要最小化）")
    logger.info("3. 尝试重启微信应用")
    logger.info("4. 检查微信版本是否为最新版本")
    logger.info("5. 确保没有其他程序占用微信窗口")
    
    logger.info("\n🔧 货主编号问题解决方案:")
    logger.info("1. 使用正确的货主编号（如 AJT-XLSWDT）")
    logger.info("2. 联系旺店通客服确认您的货主编号")
    logger.info("3. 检查API权限是否包含出库单查询")
    logger.info("4. 考虑使用其他查询方式（如原始订单号）")

if __name__ == '__main__':
    print("🔍 开始调试微信窗口和货主编号问题...")
    print("\n请选择调试模式:")
    print("1. 调试微信窗口问题")
    print("2. 调试货主编号问题") 
    print("3. 查看解决方案建议")
    print("4. 全部调试")
    
    choice = input("请输入选择 (1/2/3/4): ").strip()
    
    if choice == "1":
        debug_wechat_windows()
    elif choice == "2":
        debug_owner_no_issue()
    elif choice == "3":
        provide_solutions()
    elif choice == "4":
        debug_wechat_windows()
        debug_owner_no_issue()
        provide_solutions()
    else:
        print("无效选择，退出程序")
    
    print("\n🎯 调试完成")
