#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
旺店通数据自动获取工具打包脚本
自动化构建可执行文件
"""

import os
import sys
import shutil
import subprocess
import time
from datetime import datetime

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step}: {message}")
    print(f"{'='*60}")

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n🔧 {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 错误: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def check_dependencies():
    """检查依赖是否安装"""
    print_step(1, "检查依赖")

    # 检查包的映射关系
    package_imports = {
        'requests': 'requests',
        'openpyxl': 'openpyxl',
        'python-dotenv': 'dotenv',
        'pyinstaller': 'PyInstaller'
    }

    missing_packages = []

    for package, import_name in package_imports.items():
        try:
            __import__(import_name)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")

    if missing_packages:
        print(f"\n📦 安装缺失的依赖包...")
        install_cmd = f"pip install {' '.join(missing_packages)}"
        if not run_command(install_cmd, "安装依赖包"):
            print("❌ 依赖安装失败，请手动安装")
            return False

        # 重新检查安装结果
        print("\n🔍 重新检查依赖...")
        for package in missing_packages:
            import_name = package_imports[package]
            try:
                __import__(import_name)
                print(f"✅ {package} 安装成功")
            except ImportError:
                print(f"❌ {package} 安装失败")
                return False

    print("✅ 所有依赖检查完成")
    return True

def clean_build():
    """清理构建目录"""
    print_step(2, "清理构建目录")

    dirs_to_clean = ['build', 'dist']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🗑️ 删除目录: {dir_name}")
            try:
                shutil.rmtree(dir_name)
            except Exception as e:
                print(f"⚠️ 删除目录失败: {e}")

    # 清理Python缓存文件
    try:
        for root, dirs, files in os.walk('.'):
            # 删除.pyc和.pyo文件
            for file in files:
                if file.endswith(('.pyc', '.pyo')):
                    file_path = os.path.join(root, file)
                    try:
                        print(f"🗑️ 删除文件: {file_path}")
                        os.remove(file_path)
                    except Exception as e:
                        print(f"⚠️ 删除文件失败: {e}")

            # 删除__pycache__目录
            for dir_name in dirs[:]:  # 使用切片复制避免修改正在迭代的列表
                if dir_name == '__pycache__':
                    cache_dir = os.path.join(root, dir_name)
                    try:
                        print(f"🗑️ 删除缓存目录: {cache_dir}")
                        shutil.rmtree(cache_dir)
                        dirs.remove(dir_name)  # 从列表中移除已删除的目录
                    except Exception as e:
                        print(f"⚠️ 删除缓存目录失败: {e}")
    except Exception as e:
        print(f"⚠️ 清理缓存时出错: {e}")

    print("✅ 构建目录清理完成")
    return True

def validate_files():
    """验证必要文件是否存在"""
    print_step(3, "验证项目文件")
    
    required_files = [
        'wdt_data_gui.py',
        'export_to_wdt_data.py', 
        'wdt_post_client.py',
        'config.py',
        'wdt_data_gui.spec'
    ]
    
    missing_files = []
    
    for file_name in required_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name} 存在")
        else:
            missing_files.append(file_name)
            print(f"❌ {file_name} 缺失")
    
    if missing_files:
        print(f"❌ 缺失必要文件: {missing_files}")
        return False
    
    print("✅ 项目文件验证完成")
    return True

def build_executable():
    """构建可执行文件"""
    print_step(4, "构建可执行文件")
    
    # 使用PyInstaller构建
    build_cmd = "pyinstaller wdt_data_gui.spec --clean --noconfirm"
    
    if not run_command(build_cmd, "使用PyInstaller构建"):
        return False
    
    # 检查输出文件
    exe_path = os.path.join('dist', '旺店通数据自动获取工具.exe')
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"✅ 可执行文件构建成功")
        print(f"📁 文件路径: {exe_path}")
        print(f"📊 文件大小: {file_size:.1f} MB")
        return True
    else:
        print(f"❌ 可执行文件未找到: {exe_path}")
        return False

def create_package():
    """创建发布包"""
    print_step(5, "创建发布包")
    
    # 创建发布目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    package_name = f"旺店通数据自动获取工具_v{timestamp}"
    package_dir = os.path.join('dist', package_name)
    
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    print(f"📁 创建发布目录: {package_dir}")
    
    # 复制可执行文件
    exe_source = os.path.join('dist', '旺店通数据自动获取工具.exe')
    exe_dest = os.path.join(package_dir, '旺店通数据自动获取工具.exe')
    shutil.copy2(exe_source, exe_dest)
    print(f"📋 复制可执行文件")
    
    # 复制配置文件示例
    config_files = [
        ('wdt_gui_config.json', '配置文件示例.json'),
        ('货品档案.xlsx', '货品档案示例.xlsx'),
    ]
    
    for source, dest in config_files:
        if os.path.exists(source):
            dest_path = os.path.join(package_dir, dest)
            shutil.copy2(source, dest_path)
            print(f"📋 复制配置文件: {dest}")
    
    # 复制说明文档
    docs_dir = os.path.join(package_dir, '使用说明')
    os.makedirs(docs_dir)
    
    doc_files = [
        'GUI使用说明.md',
        'GUI功能演示.md',
        '每日定时功能说明.md',
        '物流单号过滤功能说明.md',
        '配置保存和自动启动功能说明.md'
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            dest_path = os.path.join(docs_dir, doc_file)
            shutil.copy2(doc_file, dest_path)
            print(f"📋 复制说明文档: {doc_file}")
    
    # 创建README文件
    readme_content = f"""# 旺店通数据自动获取工具

## 📦 发布信息
- 版本: {timestamp}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 平台: Windows

## 🚀 快速开始
1. 双击运行 "旺店通数据自动获取工具.exe"
2. 首次运行会使用默认配置
3. 根据需要修改输出路径和货品档案路径
4. 程序会自动保存配置，下次启动时自动加载

## 📁 文件说明
- 旺店通数据自动获取工具.exe: 主程序
- 配置文件示例.json: 配置文件示例
- 货品档案示例.xlsx: 货品档案文件示例
- 使用说明/: 详细使用说明文档

## ⚠️ 注意事项
1. 首次运行前请准备好货品档案Excel文件
2. 确保网络连接正常，能够访问旺店通API
3. 程序会自动创建配置文件，无需手动配置

## 📞 技术支持
如有问题请查看使用说明文档或联系技术支持。

---
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    readme_path = os.path.join(package_dir, 'README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"📋 创建README文件")
    
    # 创建ZIP压缩包
    zip_name = f"{package_name}.zip"
    zip_path = os.path.join('dist', zip_name)
    
    shutil.make_archive(
        os.path.join('dist', package_name),
        'zip',
        package_dir
    )
    
    if os.path.exists(zip_path):
        zip_size = os.path.getsize(zip_path) / (1024 * 1024)  # MB
        print(f"✅ 发布包创建成功")
        print(f"📁 压缩包路径: {zip_path}")
        print(f"📊 压缩包大小: {zip_size:.1f} MB")
        return True
    else:
        print(f"❌ 发布包创建失败")
        return False

def main():
    """主函数"""
    print("🏗️ 旺店通数据自动获取工具构建脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = time.time()
    
    # 执行构建步骤
    steps = [
        (check_dependencies, "检查依赖"),
        (clean_build, "清理构建目录"),
        (validate_files, "验证项目文件"),
        (build_executable, "构建可执行文件"),
        (create_package, "创建发布包")
    ]
    
    for i, (func, desc) in enumerate(steps, 1):
        try:
            if not func():
                print(f"\n❌ 构建失败在步骤 {i}: {desc}")
                return False
        except Exception as e:
            print(f"\n❌ 构建异常在步骤 {i}: {desc}")
            print(f"错误信息: {e}")
            return False
    
    # 构建完成
    end_time = time.time()
    duration = end_time - start_time
    
    print_step("完成", "构建成功")
    print(f"🎉 构建完成！")
    print(f"⏱️ 总耗时: {duration:.1f} 秒")
    print(f"📁 输出目录: dist/")
    print(f"🚀 可以开始使用了！")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
